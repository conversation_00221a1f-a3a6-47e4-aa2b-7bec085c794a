/* --- Variables & Basic Reset --- */
:root {
    --primary-color: #6A5AE0; /* Approximate purple */
    --secondary-color: #4A4A6A; /* Dark text / icons */
    --text-color: #555;
    --text-light: #9a9a9a;
    --bg-color: #F7F8FC;
    --bg-light: #FFFFFF;
    --border-color: #EAEAEA;
    --border-light: #f0f0f0; /* Lighter border for dashed lines */
    --border-radius: 8px;
    /* Trend Colors */
    --green-color: #6AC9A9;
    --red-color: #E57373;
    --yellow-color: #FFB74D;
    --neutral-color: var(--text-light); /* Use light text for neutral */

    --warning-text-color: #c0392b; /* More distinct warning red */
    --font-family: 'Poppins', sans-serif;
    --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    --box-shadow-strong: 0 5px 20px rgba(0,0,0,0.15); /* For modals/action bar */
    --sidebar-width: 250px; /* Define sidebar width */
    --sidebar-bg: var(--bg-light);
    --sidebar-text: var(--secondary-color);
    --sidebar-text-light: var(--text-light);
    --sidebar-hover-bg: var(--bg-color);
    --sidebar-active-bg: rgba(106, 90, 224, 0.1); /* Light primary background for active */
    --sidebar-active-text: var(--primary-color);
    --sidebar-icon-color: var(--text-light);

    /* Added for consistency */
    --text-dark: var(--secondary-color);
    --text-medium: #6c757d;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

* { margin: 0; padding: 0; box-sizing: border-box; }

body {
    font-family: var(--font-family);
    background-color: var(--bg-color);
    color: var(--text-color);
    font-size: 14px;
    line-height: 1.6;
    overflow-x: hidden;
}

a { text-decoration: none; color: inherit; }
ul { list-style: none; }
button { font-family: inherit; cursor: pointer; border: none; background: none; }
input, select, textarea { font-family: inherit; font-size: 14px; }
input[type="checkbox"] { cursor: pointer; }

/* --- Base Button Style --- */
.btn { padding: 8px 15px; border-radius: var(--border-radius); font-size: 14px; font-weight: 500; display: inline-flex; align-items: center; justify-content: center; gap: 8px; transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease; cursor: pointer; border: 1px solid transparent; line-height: 1.5; text-decoration: none; white-space: nowrap; }
.btn i { line-height: 1; }
.btn:disabled { cursor: not-allowed; opacity: 0.7; }
.btn:focus { outline: none; box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.3); }
.btn-secondary { background-color: var(--bg-light); color: var(--secondary-color); border: 1px solid var(--border-color); }
.btn-secondary:hover:not(:disabled) { background-color: var(--bg-color); }
.btn-primary { background-color: var(--primary-color); color: white; border-color: var(--primary-color); }
.btn-primary:hover:not(:disabled) { background-color: #5a4bd3; border-color: #5a4bd3; }
.btn-delete, .btn-danger { background-color: var(--red-color); color: white; border-color: var(--red-color); }
.btn-delete:hover:not(:disabled), .btn-danger:hover:not(:disabled) { background-color: #d32f2f; border-color: #d32f2f; }
.btn-sm { padding: 6px 12px; font-size: 13px; }

/* --- Layout --- */
.app-container { display: flex; min-height: 100vh; }
.app-main { flex-grow: 1; padding: 25px 30px; overflow-y: auto; height: 100vh; }

/* --- Sidebar Styles --- */
.app-sidebar { width: var(--sidebar-width); background-color: var(--sidebar-bg); border-right: 1px solid var(--border-color); display: flex; flex-direction: column; padding: 20px 0 0 0; flex-shrink: 0; height: 100vh; position: sticky; top: 0; }
.sidebar-header { display: flex; align-items: center; padding: 0 20px 20px 20px; border-bottom: 1px solid var(--border-color); margin-bottom: 15px; flex-shrink: 0; }
.sidebar-header .app-logo { margin-right: 15px; }
.sidebar-header .app-logo i { font-size: 28px; color: var(--primary-color); background-color: rgba(106, 90, 224, 0.1); padding: 8px; border-radius: var(--border-radius); }
.sidebar-header .app-title { line-height: 1.3; }
.sidebar-header .app-title strong { display: block; font-weight: 600; color: var(--sidebar-text); font-size: 15px; }
.sidebar-header .app-title span { font-size: 12px; color: var(--sidebar-text-light); }
.sidebar-nav { flex-grow: 1; overflow-y: auto; padding: 0 15px; }
.sidebar-nav ul { margin: 0; padding: 0; list-style: none; }
.sidebar-nav li a { display: flex; align-items: center; padding: 10px 15px; margin-bottom: 5px; border-radius: var(--border-radius); color: var(--sidebar-text-light); font-weight: 500; transition: background-color 0.2s ease, color 0.2s ease; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.sidebar-nav li a i { font-size: 16px; color: var(--sidebar-icon-color); width: 20px; text-align: center; margin-right: 12px; flex-shrink: 0; transition: color 0.2s ease; }
.sidebar-nav li a span { flex-grow: 1; font-size: 14px; }
.sidebar-nav li a:hover { background-color: var(--sidebar-hover-bg); color: var(--sidebar-text); }
.sidebar-nav li a:hover i { color: var(--sidebar-text); }
.sidebar-nav li a.active { background-color: var(--sidebar-active-bg); color: var(--sidebar-active-text); }
.sidebar-nav li a.active i { color: var(--sidebar-active-text); }
.sidebar-footer-nav { margin-top: auto; padding: 15px 15px 15px 15px; border-top: 1px solid var(--border-color); flex-shrink: 0; }
.sidebar-footer-nav ul { margin: 0; padding: 0; list-style: none; }
.sidebar-footer-nav li a { display: flex; align-items: center; padding: 10px 15px; margin-bottom: 5px; border-radius: var(--border-radius); color: var(--sidebar-text-light); font-weight: 500; transition: background-color 0.2s ease, color 0.2s ease; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
.sidebar-footer-nav li a i { font-size: 16px; color: var(--sidebar-icon-color); width: 20px; text-align: center; margin-right: 12px; flex-shrink: 0; transition: color 0.2s ease; }
.sidebar-footer-nav li a span { flex-grow: 1; font-size: 14px; }
.sidebar-footer-nav li a:hover { background-color: var(--sidebar-hover-bg); color: var(--sidebar-text); }
.sidebar-footer-nav li a:hover i { color: var(--sidebar-text); }

/* --- Search Bar Style --- */
.search-bar { display: flex; align-items: center; background-color: var(--bg-light); padding: 8px 12px; border-radius: var(--border-radius); border: 1px solid var(--border-color); }
.search-bar .search-icon { color: var(--text-light); margin-right: 8px; }
.search-bar input { border: none; background: none; outline: none; width: 100%; font-size: 14px; }
/* Ensure the filter icon is properly positioned */
.search-bar .filter-toggle-icon { margin-left: 5px; margin-right: -5px; }

/* --- Dashboard Header in Main Content --- */
.dashboard-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; padding-bottom: 0; border-bottom: none; flex-wrap: wrap; gap: 10px; }
.dashboard-header h1 { font-size: 24px; font-weight: 600; color: var(--secondary-color); margin: 0; }
.office-header { display: flex; align-items: center; gap: 15px; width: 100%; }
.office-logo { flex-shrink: 0; }
.office-logo img { width: 50px; height: auto; }
.office-info { display: flex; flex-direction: column; }
.office-info h1 { font-size: 22px; font-weight: 600; color: var(--secondary-color); margin: 0; line-height: 1.2; }
.office-info p { font-size: 14px; color: var(--text-light); margin: 5px 0 0 0; }
.view-switch-container { display: flex; gap: 5px; background-color: var(--bg-color); border-radius: var(--border-radius); padding: 4px; border: 1px solid var(--border-color); margin-left: auto; }
.btn-view-switch { padding: 5px 12px; font-size: 13px; font-weight: 500; color: var(--text-light); background-color: transparent; border: none; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; text-decoration: none; white-space: nowrap; }
.btn-view-switch:hover { background-color: rgba(0, 0, 0, 0.05); color: var(--secondary-color); }
.btn-view-switch.active { background-color: var(--primary-color); color: white; }

/* --- DB Error Message --- */
.db-error-message { padding: 15px; background-color: #ffebee; border: 1px solid var(--red-color); color: var(--warning-text-color); border-radius: var(--border-radius); margin-bottom: 20px; font-size: 14px; }
.db-error-message strong { color: #c0392b; }

/* --- Stats Cards --- */
.stats-cards { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin-bottom: 25px; }
.stat-card { background-color: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-color); display: flex; flex-direction: column; gap: 8px; }
.stat-card .card-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: 5px; position: relative; }
.stat-card h4 { font-size: 13px; font-weight: 500; color: var(--secondary-color); margin: 0; line-height: 1.3; }
.stat-card-icon { width: 36px; height: 36px; border-radius: 50%; display: flex; justify-content: center; align-items: center; font-size: 16px; flex-shrink: 0; background-color: rgba(106, 90, 224, 0.1); color: var(--primary-color); transition: transform 0.2s ease, background-color 0.2s ease; }
.stat-card-icon.icon-tasks { background-color: rgba(106, 90, 224, 0.15); color: var(--primary-color); }
.stat-card-icon.icon-users { background-color: rgba(255, 183, 77, 0.15); color: var(--yellow-color); }
.stat-card-icon.icon-tags { background-color: rgba(106, 201, 169, 0.15); color: var(--green-color); }
.stat-card-icon.icon-building { background-color: rgba(229, 115, 115, 0.15); color: var(--red-color); }
.stat-card-icon.icon-map-marker-alt { background-color: rgba(90, 180, 224, 0.15); color: #5AB4E0; }
.stat-card-icon.icon-map-pin { background-color: rgba(156, 106, 201, 0.15); color: #9C6AC9; }
.stat-card-icon.icon-map { background-color: rgba(77, 182, 172, 0.15); color: #4DB6AC; }
.stat-card-icon.icon-map-signs { background-color: rgba(255, 167, 38, 0.15); color: #FFA726; }
.stat-card-icon.icon-database { background-color: rgba(120, 120, 120, 0.15); color: var(--text-light); }
.stat-card-icon.icon-exclamation-circle { background-color: rgba(255, 183, 77, 0.15); color: var(--yellow-color); }
.stat-card-icon.icon-exclamation-triangle { background-color: rgba(229, 115, 115, 0.15); color: var(--red-color); }
.stat-card .card-body { display: flex; flex-direction: column; gap: 5px; flex-grow: 1; justify-content: space-between; }
.stat-card .value { font-size: 26px; font-weight: 600; color: var(--secondary-color); margin: 0; line-height: 1.2; }
.stat-insight { font-size: 12px; font-weight: 500; display: flex; align-items: center; gap: 5px; margin-top: 5px; line-height: 1.4; }
.stat-insight i { font-size: 11px; }
.stat-insight span { display: inline-block; max-width: 100%; }
.text-green { color: var(--green-color); }
.text-red { color: var(--red-color); }
.text-neutral { color: var(--neutral-color); }

/* --- General Card Styling --- */
.card { background-color: var(--bg-light); padding: 20px; border-radius: var(--border-radius); border: 1px solid var(--border-color); margin-bottom: 25px; }

/* --- Activities & Participants Table Headers --- */
.activities-header, .participants-header, .letters-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 10px; }
.activities-header h2, .participants-header h2, .letters-header h2 { font-size: 18px; font-weight: 600; color: var(--secondary-color); margin-bottom: 0; }
.table-controls { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; padding-bottom: 15px; border-bottom: 1px solid var(--border-color); flex-wrap: wrap; gap: 10px; }
.filter-buttons { display: flex; gap: 10px; flex-wrap: wrap; }
.btn-filter { background-color: var(--bg-light); color: var(--text-light); border: 1px solid var(--border-color); padding: 6px 12px; font-size: 13px; }
.btn-filter.active, .btn-filter:hover { background-color: var(--bg-color); color: var(--secondary-color); border-color: #ccc; }
.table-right-controls { display: flex; align-items: center; gap: 15px; margin-left: auto; flex-wrap: wrap; }
.table-controls .search-form { display: flex; }
.table-controls .search-bar { width: 220px; }
.results-per-page-form { display: flex; align-items: center; gap: 8px; font-size: 13px; color: var(--text-light); margin-right: auto; }
.results-per-page-form select { padding: 4px 8px; border-radius: 4px; border: 1px solid var(--border-color); font-size: 13px; background-color: var(--bg-light); cursor: pointer; }

/* Table Styles */
.table-container { overflow-x: auto; }
table { width: 100%; border-collapse: collapse; white-space: nowrap; }
thead { border-bottom: 1px solid var(--border-color); }
th { text-align: left; padding: 12px 15px; font-size: 12px; font-weight: 600; color: var(--text-light); text-transform: uppercase; }
tbody tr { border-bottom: 1px solid var(--border-color); transition: background-color 0.15s ease; }
tbody tr:last-child { border-bottom: none; }
tbody tr:hover { background-color: var(--bg-color); }
td { padding: 12px 15px; vertical-align: middle; font-size: 13px; }
th.col-checkbox, td.col-checkbox { width: 40px; text-align: center; padding-left: 10px; padding-right: 10px; }
td.col-checkbox input[type="checkbox"] { vertical-align: middle; }
th.col-rownum, td.col-rownum { width: 50px; text-align: center; padding-left: 5px; padding-right: 5px; color: var(--text-light); }
.date-col { color: var(--text-light); }
.participants-col { text-align: right; padding-right: 20px; font-weight: 500; }
.table-message { text-align: center; padding: 20px; color: var(--text-light); }
.table-message.error { color: var(--warning-text-color); }
.table-message i { margin-right: 8px; vertical-align: middle; }
.inactive-row { opacity: 0.6; background-color: #f9f9f9; }

/* --- Pagination --- */
.pagination-controls { display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; padding-top: 20px; margin-top: 15px; border-top: 1px solid var(--border-color); gap: 15px; }
.pagination-controls .results-per-page { display: flex; align-items: center; gap: 8px; font-size: 13px; color: var(--text-light); }
.pagination-controls .results-per-page label { white-space: nowrap; }
.pagination-controls .results-per-page select { padding: 4px 8px; border-radius: 4px; border: 1px solid var(--border-color); font-size: 13px; background-color: var(--bg-light); cursor: pointer; }
.pagination-info { white-space: nowrap; color: #666; font-size: 14px; font-weight: 400; }
.pagination-nav { display: flex; align-items: center; gap: 5px; flex-wrap: wrap; justify-content: flex-end; }
.pagination-nav .btn-nav, .pagination-nav .btn-page { padding: 6px 10px; font-size: 13px; color: var(--text-light); background-color: var(--bg-light); border: 1px solid var(--border-color); border-radius: var(--border-radius); }
.pagination-nav .disabled { color: #ccc !important; cursor: not-allowed !important; background-color: var(--bg-light) !important; pointer-events: none; border-color: var(--border-color) !important; opacity: 0.7; }
.pagination-nav .btn-page.active { background-color: var(--primary-color) !important; color: var(--bg-light) !important; border-color: var(--primary-color) !important; font-weight: 600; }
.pagination-nav .btn-nav:not(.disabled):hover, .pagination-nav .btn-page:not(.active):not(.disabled):hover { background-color: var(--bg-color) !important; color: var(--secondary-color) !important; }

/* --- Floating Action Bar --- */
#selectionActionBar { position: fixed; bottom: 20px; left: calc(var(--sidebar-width) + ((100% - var(--sidebar-width)) / 2)); transform: translateX(-50%); background-color: var(--bg-light); padding: 10px 20px; border-radius: var(--border-radius); box-shadow: var(--box-shadow-strong); z-index: 101; display: none; align-items: center; gap: 15px; border: 1px solid var(--border-color); animation: slideUpFadeIn 0.3s ease-out; }
#selectionActionBar.visible { display: flex; }
#selectedCount { font-weight: 500; color: var(--secondary-color); white-space: nowrap; margin-right: 10px; }
.action-buttons { display: flex; gap: 8px; }
.action-buttons .btn { padding: 6px 12px; font-size: 13px; }
.close-action-bar { background: none; border: none; font-size: 18px; color: var(--text-light); cursor: pointer; padding: 0 5px; margin-left: 10px; line-height: 1; }
.close-action-bar:hover { color: var(--secondary-color); }

/* --- Modals --- */
.modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; overflow: hidden; background-color: rgba(0,0,0,0.5); align-items: center; justify-content: center; padding: 20px; }
.modal.visible { display: flex; }
.modal-content { background-color: var(--bg-light); margin: auto; padding: 0; border: 1px solid var(--border-color); width: 90%; max-width: 800px; border-radius: var(--border-radius); box-shadow: var(--box-shadow-strong); animation: fadeInModal 0.3s ease-out; display: flex; flex-direction: column; max-height: calc(100vh - 40px); }
.modal-content.small { max-width: 500px; width: 80%; }
.modal-content.large { max-width: 1000px; } /* Increased max-width for large modals */
.modal-header { padding: 15px 20px; border-bottom: 1px solid var(--border-color); display: flex; justify-content: space-between; align-items: center; flex-shrink: 0; }
.modal-header h2 { margin: 0; font-size: 18px; font-weight: 600; color: var(--secondary-color); line-height: 1.4; }
.modal-header h2 i { margin-right: 8px; color: var(--primary-color); } /* Style modal header icons */
.close-modal { color: var(--text-light); background: none; border: none; font-size: 24px; font-weight: bold; cursor: pointer; line-height: 1; padding: 0 5px; transition: color 0.2s ease; }
.close-modal:hover, .close-modal:focus { color: var(--secondary-color); }
.modal-body { padding: 20px; line-height: 1.6; overflow-y: auto; flex-grow: 1; }
.modal-body p { margin-bottom: 10px; }
.modal-body strong { word-break: break-word; }
.form-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px 20px; }
.form-field.full-width { grid-column: span 4; }
.form-field.half-width { grid-column: span 2; }
.form-field.quarter-width { grid-column: span 1; }
.form-field.remarks-field { grid-column: span 4; }
.form-field.remarks-field textarea { min-height: 80px; }
.modal-body .form-field label, .modal-body .form-group label { display: block; margin-bottom: 5px; font-weight: 500; font-size: 13px; color: var(--secondary-color); }
.modal-body input[type=text], .modal-body input[type=number], .modal-body input[type=date], .modal-body input[type=file], .modal-body input[type=email], .modal-body input[type=password], .modal-body select, .modal-body textarea { width: 100%; padding: 8px 10px; margin-bottom: 0; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; font-size: 14px; transition: border-color 0.2s ease, box-shadow 0.2s ease; }
.modal-body input:focus, .modal-body select:focus, .modal-body textarea:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2); }
.modal-body textarea { min-height: 60px; resize: vertical; }
.modal-body select { -webkit-appearance: none; -moz-appearance: none; appearance: none; background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23666%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'); background-repeat: no-repeat; background-position: right 10px top 50%; background-size: 10px auto; padding-right: 30px; background-color: var(--bg-light); }
.modal-footer { padding: 15px 20px; border-top: 1px solid var(--border-color); text-align: right; background-color: var(--bg-color); border-bottom-left-radius: var(--border-radius); border-bottom-right-radius: var(--border-radius); flex-shrink: 0; }
.modal-footer .btn { margin-left: 10px; font-size: 14px; padding: 8px 15px; font-weight: 500; line-height: 1.5; white-space: nowrap; }
#editModal .modal-footer .btn.btn-secondary { font-size: 14px; padding: 8px 15px; font-weight: 500; }

/* Loading / Error Indicators */
.loading-indicator, .error-indicator { text-align: center; padding: 20px; font-size: 14px; color: var(--text-light); display: flex; align-items: center; justify-content: center; gap: 8px; }
.loading-indicator i.fa-spinner { color: var(--primary-color); animation: spin 1.5s linear infinite; }
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
.error-indicator { color: var(--warning-text-color); padding: 10px; border: 1px solid var(--red-color); border-radius: 4px; margin-bottom: 15px; text-align: left; background-color: #ffebee; justify-content: flex-start; }
.error-indicator i { color: var(--red-color); font-size: 14px; }

/* View Files List */
/* Grouped View */
#viewFilesModal .modal-body { padding-top: 15px; }
.view-files-header { display: flex; justify-content: space-between; align-items: center; padding-bottom: 15px; margin-bottom: 15px; border-bottom: 1px solid var(--border-color); }
.view-files-header p { margin: 0; font-weight: 500; color: var(--text-dark); }
.view-files-controls { display: flex; align-items: center; gap: 15px; }
.view-files-controls .checkbox-label { display: flex; align-items: center; cursor: pointer; font-size: 14px; margin-bottom: 0; }
.view-files-controls input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); }
.view-files-controls .btn-delete { padding: 5px 10px; font-size: 13px; }
#fileListContainer.grouped { max-height: 55vh; overflow-y: auto; padding: 0 5px 5px 5px; border: none; background: none; } /* Adjusted for grouped view */
.file-group { margin-bottom: 15px; border: 1px solid var(--border-color); border-radius: 4px; background-color: #fff; transition: background-color 0.2s ease; }
.file-group:last-child { margin-bottom: 5px; }
.file-group-header { display: flex; align-items: center; padding: 10px 15px; background-color: #f8f9fa; border-bottom: 1px solid var(--border-color); cursor: pointer; position: sticky; top: 0; z-index: 1; }
.file-group.collapsed + .file-group .file-group-header { border-top: none; }
.file-group-header .toggle-group { background: none; border: none; padding: 0 10px 0 0; margin: 0; cursor: pointer; color: var(--primary-color); font-size: 14px; transition: transform 0.2s ease; line-height: 1; }
.file-group.collapsed .toggle-group i { transform: rotate(-90deg); }
.file-group:not(.collapsed) .toggle-group i { transform: rotate(0deg); }
.file-group-header .group-title { font-weight: 600; color: var(--text-dark); flex-grow: 1; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; margin-right: 10px; }
.file-group-header .badge { background-color: var(--primary-color); color: white; padding: 3px 8px; border-radius: 10px; font-size: 12px; font-weight: 500; flex-shrink: 0; }
.file-group-content { padding: 0px 15px 5px 15px; display: block; transition: max-height 0.3s ease-out, opacity 0.3s ease-out; max-height: 1000px; overflow: hidden; }
.file-group.collapsed .file-group-content { max-height: 0; opacity: 0; padding-top: 0; padding-bottom: 0; border-top: none; }
.file-item { display: flex; align-items: center; padding: 8px 0; border-bottom: 1px dashed var(--border-light); gap: 10px; }
.file-item:last-child { border-bottom: none; padding-bottom: 10px; }
.file-group.collapsed .file-item { display: none; }
.file-item input[type="checkbox"] { margin-right: 5px; accent-color: var(--primary-color); flex-shrink: 0; vertical-align: middle; margin-top: 0; }
.file-item .file-icon { font-size: 1.4em; color: #6c757d; width: 25px; text-align: center; flex-shrink: 0; }
.file-item .fa-file-pdf { color: #dc3545; } .file-item .fa-file-word { color: #0d6efd; } .file-item .fa-file-excel { color: #198754; } .file-item .fa-file-image { color: #ffc107; } .file-item .fa-file-archive { color: #6f42c1; }
.file-item .file-details { flex-grow: 1; display: flex; flex-direction: column; overflow: hidden; min-width: 0; }
.file-item .file-name { font-weight: 500; color: var(--text-dark); white-space: nowrap; overflow: hidden; text-overflow: ellipsis; display: inline-block; }
.file-item .file-meta { font-size: 12px; color: var(--text-light); }
.file-item .file-size { font-size: 13px; color: var(--text-medium); white-space: nowrap; flex-shrink: 0; min-width: 80px; text-align: right; }
.file-item .btn-icon { background: none; border: none; color: var(--text-light); cursor: pointer; padding: 3px; font-size: 14px; flex-shrink: 0; margin-left: 5px; }
.file-item .btn-icon:hover { color: var(--primary-color); }
.file-item .btn-preview-file { color: #6c757d; }
.file-item .btn-preview-file:hover { color: #0d6efd; }
#viewFilesModal .loading-indicator.active { display: flex; } /* Corrected loading indicator */
#viewFilesModal .empty-message, #viewFilesModal .error-message { text-align: center; padding: 30px; color: var(--text-light); display: none; }
#viewFilesModal .error-message { color: var(--red-color); }
#viewFilesSelectAll:indeterminate { accent-color: var(--primary-color); }

/* Custom Notification */
.notification { display: none; position: fixed; top: 20px; right: 20px; z-index: 2000; background-color: #fff; border-radius: var(--border-radius); box-shadow: 0 4px 12px rgba(0,0,0,0.15); padding: 15px 20px; max-width: 350px; opacity: 0; transform: translateX(100%); transition: opacity 0.3s ease-out, transform 0.3s ease-out; border-left: 4px solid var(--primary-color); }
.notification.visible { display: block; opacity: 1; transform: translateX(0); }
.notification.success { border-left-color: var(--green-color); }
.notification.error { border-left-color: var(--red-color); }
.notification.warning { border-left-color: var(--yellow-color); }
.notification-message { margin: 0; font-size: 14px; }

/* --- Animations --- */
@keyframes fadeInModal { from { opacity: 0; transform: scale(0.95) translateY(-10px); } to { opacity: 1; transform: scale(1) translateY(0); } }
@keyframes slideUpFadeIn { from { opacity: 0; transform: translate(-50%, 20px); } to { opacity: 1; transform: translate(-50%, 0); } }
@keyframes typing { from { width: 0; } to { width: 100%; } }
@keyframes blinkCaret { from, to { border-right-color: transparent; } 50% { border-right-color: var(--primary-color); } }
@keyframes fadeInUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }
@keyframes pulse { 0% { transform: scale(1); } 50% { transform: scale(1.05); } 100% { transform: scale(1); } }
@keyframes slideInRight { from { opacity: 0; transform: translateX(20px); } to { opacity: 1; transform: translateX(0); } }

/* --- Import Modal Specific Styles --- */
.file-upload-area { margin-top: 10px; padding: 15px; border: 2px dashed var(--border-color); border-radius: var(--border-radius); background-color: #fdfdfd; display: flex; align-items: center; gap: 10px; }
.file-upload-area label.btn { flex-shrink: 0; }
.file-name-display { color: var(--text-light); font-style: italic; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; flex-grow: 1; }
.required-columns-info .column-tags { margin-top: 8px; display: flex; flex-wrap: wrap; gap: 6px; }
.required-columns-info .tag { background-color: var(--bg-color); color: var(--text-color); padding: 3px 8px; border-radius: 4px; font-size: 12px; border: 1px solid var(--border-color); }
#previewTableContainer table { font-size: 12px; }
#previewTableContainer th, #previewTableContainer td { padding: 8px 10px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 150px; }
#previewTableContainer thead { background-color: var(--bg-color); position: sticky; top: 0; z-index: 1; }
#importResultIndicator.success-indicator { background-color: #e8f5e9; color: #388e3c; border: 1px solid var(--green-color); padding: 10px; border-radius: 4px; text-align: center; }
#importResultIndicator.error-indicator, #importErrorStep2.error-indicator, #importErrorStep1.error-indicator { background-color: #ffebee; color: #c62828; border: 1px solid var(--red-color); padding: 10px; border-radius: 4px; text-align: center; }
#importErrorStep1.error-indicator, #importErrorStep2.error-indicator { margin-bottom: 0; } /* Adjust specific error indicators */

/* --- Target Management Modal --- */
#manageTargetsModal .modal-content { max-width: 950px; }
#manageTargetsModal .modal-body { padding-top: 10px; }
#existingTargetsTableContainer { max-height: 300px; overflow-y: auto; margin-bottom: 20px; border: 1px solid var(--border-color); }
#existingTargetsTable { width: 100%; border-collapse: collapse; }
#existingTargetsTable th, #existingTargetsTable td { padding: 8px 10px; font-size: 13px; text-align: left; border-bottom: 1px solid var(--border-light); }
#existingTargetsTable th { font-weight: 600; color: var(--text-light); background-color: var(--bg-color); }
#existingTargetsTable thead { position: sticky; top: 0; background-color: var(--bg-color); z-index: 1; }
#existingTargetsTable .actions-col { width: 80px; text-align: center; }
#existingTargetsTable .btn-icon { background: none; border: none; padding: 3px; font-size: 14px; cursor: pointer; color: var(--text-light); }
#existingTargetsTable .btn-icon.edit-target:hover { color: var(--primary-color); }
#existingTargetsTable .btn-icon.delete-target:hover { color: var(--red-color); }
#addTargetFormContainer { margin-top: 15px; padding-top: 15px; border-top: 1px solid var(--border-color); }
#addTargetFormContainer h4 { margin-bottom: 15px; font-weight: 600; color: var(--secondary-color); }
#addTargetForm .form-grid { grid-template-columns: repeat(5, 1fr); gap: 15px; } /* 5 columns for better layout */
#addTargetForm .category-field { grid-column: span 1; }
#addTargetForm .subcategory-field { grid-column: span 2; }
#addTargetForm .indicator-field { grid-column: span 2; }
#addTargetForm .year-field { grid-column: span 1; }
#addTargetForm .target-field { grid-column: span 1; }
#addTargetForm .form-field label { margin-bottom: 3px; font-size: 12px; }
#addTargetForm input[readonly] { background-color: #eee; cursor: not-allowed; }
#addTargetForm .required { color: var(--red-color); margin-left: 2px;}
#addTargetForm .form-field small { font-size: 11px; color: var(--text-light); margin-top: 2px; display: block;}
.modal-footer.target-modal-footer { display: flex; justify-content: space-between; align-items: center; }
.target-modal-footer .left-buttons { display: flex; gap: 10px; }
.target-modal-footer .right-buttons { display: flex; gap: 10px; }

/* --- Report/Graph Styles --- */
#reportGraphContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 20px; /* Gap between graphs */
    margin-top: 20px; /* Adjust as needed */
    padding: 0; /* Remove padding if graphs are the only content */
    background-color: transparent; /* Assuming graphs are the content */
    border: none; /* Assuming graphs are the content */
}
.graph-wrapper {
    background-color: var(--bg-light);
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
    /* box-shadow removed as requested */
    flex: 1 1 calc(50% - 20px); /* Two columns, accounting for gap */
    min-width: 300px; /* Minimum width before wrapping */
    display: flex;
    flex-direction: column;
    height: 350px; /* Fixed height for consistency */
}
.graph-wrapper h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--secondary-color);
    margin-bottom: 15px;
    text-align: center;
    white-space: normal; /* Allow title to wrap */
}
.graph-wrapper canvas {
    max-width: 100%;
    flex-grow: 1; /* Allow canvas to take remaining space */
    height: auto !important; /* Ensure responsive height within flex */
    min-height: 200px; /* Prevent collapsing too much */
}

/* --- Navigation Tabs --- */
.nav-tabs-container { margin-bottom: 20px; }
.nav-tabs { display: flex; border-bottom: 1px solid #ddd; margin-bottom: 0; background-color: #f8f9fa; border-radius: 4px 4px 0 0; justify-content: space-between; flex-wrap: wrap; /* Allow wrapping */ }
.nav-tabs-left { display: flex; flex-grow: 1; /* Allow left side to grow */ }
.nav-tabs-right { display: flex; align-items: center; padding-right: 10px; gap: 8px; flex-shrink: 0; /* Prevent shrinking */ padding-top: 5px; padding-bottom: 5px; }
.nav-tab { padding: 12px 20px; color: #495057; text-decoration: none; font-weight: 500; border-bottom: 3px solid transparent; transition: all 0.2s ease; cursor: pointer; white-space: nowrap; }
.nav-tab:hover { color: var(--primary-color); background-color: rgba(0, 123, 255, 0.05); }
.nav-tab.active { color: var(--primary-color); border-bottom: 3px solid var(--primary-color); }
.tab-content { display: none; padding: 0; } /* Remove padding from tab-content itself */
.tab-content.active { display: block; }
.nav-tabs-right .btn-secondary { background-color: transparent; border-color: #ddd; color: #495057; }
.nav-tabs-right .btn-secondary:hover { background-color: rgba(0, 123, 255, 0.05); color: var(--primary-color); }
.nav-tabs-right .btn-primary { background-color: var(--primary-color); border-color: var(--primary-color); color: white; }
.nav-tabs-right .btn-primary:hover { background-color: #5a4bd3; border-color: #5a4bd3; }

/* Style for the report year filter */
#reportYearFilter {
    padding: 5px 8px; /* Slightly smaller padding */
    font-size: 13px;
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
    background-color: white;
    cursor: pointer;
    margin-left: 5px; /* Space from Manage Targets button */
    height: 31px; /* Match btn-sm height approx */
    vertical-align: middle; /* Align with buttons */
}
#reportYearFilter:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2);
}


/* --- Responsive Adjustments --- */
@media (max-width: 1200px) {
    .stats-cards { grid-template-columns: repeat(3, 1fr); }
}
@media (max-width: 992px) {
    :root { --sidebar-width: 220px; }
    .app-main { padding: 20px; }
    #selectionActionBar { left: calc(var(--sidebar-width) + ((100% - var(--sidebar-width)) / 2)); }
    .stats-cards { grid-template-columns: repeat(2, 1fr); }
    .dashboard-header h1 { order: 1; }
    .dashboard-header .view-switch-container { order: 2; margin-left: 0; }
    #addTargetForm .form-grid { grid-template-columns: repeat(2, 1fr); } /* Adjust target form */
    #addTargetForm .category-field,
    #addTargetForm .year-field,
    #addTargetForm .target-field { grid-column: span 1; }
    #addTargetForm .subcategory-field,
    #addTargetForm .indicator-field { grid-column: span 2; }
}
@media (max-width: 768px) {
    .app-sidebar { width: 60px; overflow: hidden; padding-top: 15px; }
    .app-sidebar .sidebar-header .app-title { display: none; }
    .app-sidebar .sidebar-header .app-logo { margin-right: 0; }
    .app-sidebar .sidebar-nav li a span { display: none; }
    .app-sidebar .sidebar-nav li a { justify-content: center; }
    .app-sidebar .sidebar-nav li a i { margin-right: 0; }
    .app-sidebar .sidebar-header { padding: 15px; justify-content: center; }
    .app-sidebar .sidebar-nav { padding: 0 5px; }
    .app-sidebar .sidebar-footer-nav { padding: 10px 5px; }
    .office-logo img { width: 40px; }
    .office-info h1 { font-size: 18px; }
    .office-info p { font-size: 12px; }
    .app-sidebar .sidebar-footer-nav li a span { display: none; }
    .app-sidebar .sidebar-footer-nav li a { justify-content: center; }
    .app-sidebar .sidebar-footer-nav li a i { margin-right: 0; }
    .app-main { padding: 15px; }
    .stats-cards { grid-template-columns: repeat(2, 1fr); }
    .stat-card .value { font-size: 24px; }
    .stat-insight { font-size: 11px; }
    .pagination-controls { flex-direction: column; align-items: stretch; }
    .pagination-controls .results-per-page { order: 2; text-align: center; margin-top: 10px; justify-content: center;}
    .pagination-controls .pagination-nav { order: 1; flex-grow: 1; justify-content: center; }
    .pagination-info { margin-left: 0; }
    .table-controls { flex-direction: column; align-items: stretch; }
    .filter-buttons { justify-content: center; order: 1; }
    .results-per-page-form { width: 100%; order: 2; margin-bottom: 10px; }
    .table-right-controls { flex-direction: column; width: 100%; margin-left: 0; gap: 10px; order: 3; }
    .table-controls .search-form { width: 100%; }
    .table-controls .search-bar { width: 100%; }
    #selectionActionBar { width: calc(100% - 40px); left: 20px; transform: translateX(0); padding: 10px; gap: 10px; bottom: 10px; flex-wrap: wrap; justify-content: space-between; }
    #selectedCount { margin-right: 5px; flex-basis: 100%; text-align: center; margin-bottom: 5px;}
    .action-buttons { flex-grow: 1; justify-content: center; flex-wrap: wrap; gap: 5px; }
    .close-action-bar { margin-left: auto; order: -1; }
    .modal-content { max-height: 85vh; }
    .modal-body .form-grid { grid-template-columns: repeat(2, 1fr); }
    .form-field.full-width, .form-field.remarks-field { grid-column: span 2; }
    .form-field.half-width, .form-field.quarter-width { grid-column: span 1; }
    .dashboard-header { width: 100%; margin-bottom: 15px; }
    .view-switch-container { margin-left: 0; }
    .graph-wrapper { flex-basis: 100%; height: 300px; } /* One column graph */
     #addTargetForm .form-grid { grid-template-columns: 1fr; } /* Stack target form fields */
     #addTargetForm .category-field,
     #addTargetForm .subcategory-field,
     #addTargetForm .indicator-field,
     #addTargetForm .year-field,
     #addTargetForm .target-field { grid-column: span 1; }
    .nav-tabs-left { flex-basis: 100%; /* Make tabs take full width */ justify-content: center; /* Center tabs */ }
    .nav-tabs-right { flex-basis: 100%; justify-content: center; /* Center buttons */ margin-top: 5px;}
}
@media (max-width: 576px) {
    .stats-cards { grid-template-columns: 1fr; }
    .dashboard-header { flex-direction: column; align-items: flex-start; gap: 5px; }
    .office-header { flex-direction: column; align-items: flex-start; gap: 10px; }
    .office-logo img { width: 45px; }
    .stat-card .value { font-size: 22px; }
    td, th { padding: 10px 8px; font-size: 12px; }
    .participants-col { padding-right: 10px;}
    th.col-checkbox, td.col-checkbox { width: 35px; padding-left: 8px; padding-right: 8px;}
    th.col-rownum, td.col-rownum { width: 40px; padding-left: 5px; padding-right: 5px;}
    #selectionActionBar { flex-direction: column; align-items: center; gap: 8px; bottom: 10px; }
    .action-buttons { justify-content: center; flex-wrap: wrap; gap: 5px; width: 100%; }
    .action-buttons .btn { flex-grow: 1; min-width: 100px; }
    .close-action-bar { margin-left: 0; margin-top: 5px; order: 0; }
    #selectedCount { order: -1; }
    .modal-body .form-grid { grid-template-columns: 1fr; }
    .form-field.full-width, .form-field.half-width, .form-field.quarter-width, .form-field.remarks-field { grid-column: span 1; }
    .modal-footer { text-align: center; }
    .modal-footer .btn { display: block; width: 100%; margin: 8px 0; }
    #editModal .modal-footer .btn { font-size: 14px; padding: 10px 15px; }
    .view-switch-container { margin-top: 5px; width: auto; }
    .app-sidebar { width: 60px; }
    .app-sidebar .sidebar-header .app-title { display: none; }
    .app-sidebar .sidebar-nav li a span { display: none; }
    .app-sidebar .sidebar-nav li a { justify-content: center; }
    .app-sidebar .sidebar-nav li a i { margin-right: 0; }
    .app-sidebar .sidebar-footer-nav li a span { display: none; }
    .app-sidebar .sidebar-footer-nav li a { justify-content: center; }
    .app-sidebar .sidebar-footer-nav li a i { margin-right: 0; }
     #reportGraphContainer { padding: 10px; } /* Reduce padding on small screens */
     .graph-wrapper { height: 280px; } /* Further reduce graph height */
     .modal-footer.target-modal-footer { flex-direction: column-reverse; gap: 10px; }
     .target-modal-footer .left-buttons, .target-modal-footer .right-buttons { width: 100%; display: flex; justify-content: center; }
     .target-modal-footer .btn { width: 48%; }
     .nav-tabs-right { gap: 5px;} /* Reduce gap between buttons */
     #reportYearFilter { margin-left: 0; } /* Remove left margin on small screens */
}

.sidebar-footer-nav li {
    position: relative; /* Needed for absolute positioning of popup */
}
.settings-popup {
    display: none; /* Hidden by default */
    position: absolute;
    bottom: 100%; /* Position above the settings link */
    left: 15px; /* Align with sidebar padding */
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1100; /* Ensure it's above sidebar content */
    min-width: 160px;
    padding: 8px 0;
    margin-bottom: 5px; /* Space between link and popup */
    animation: fadeInPopup 0.2s ease-out;
}
.settings-popup.visible {
    display: block;
}
.settings-popup a {
    display: block;
    padding: 8px 15px;
    font-size: 14px;
    color: var(--sidebar-text);
    text-decoration: none;
    white-space: nowrap;
    transition: background-color 0.2s ease, color 0.2s ease;
}
.settings-popup a:hover {
    background-color: var(--sidebar-hover-bg);
    color: var(--sidebar-active-text);
}
.settings-popup a i {
    margin-right: 10px;
    width: 16px;
    text-align: center;
    color: var(--sidebar-icon-color);
}
.settings-popup a:hover i {
     color: var(--sidebar-active-text);
}
@keyframes fadeInPopup {
    from { opacity: 0; transform: translateY(5px); }
    to { opacity: 1; transform: translateY(0); }
}
/* --- End Settings Popup Styles --- */

/* Update Profile Modal Specifics */
#updateProfileForm .form-grid {
    grid-template-columns: 1fr; /* Simpler layout */
    gap: 15px;
}
#updateProfileForm .password-section {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid var(--border-light);
}
 #updateProfileForm .form-field small {
    font-size: 11px;
    color: var(--text-light);
    margin-top: 3px;
    display: block;
}
/* Logout Confirm Modal Specifics */
#logoutConfirmModal .modal-content {
     max-width: 400px;
}
#logoutConfirmModal .modal-body {
     text-align: center;
     font-size: 16px;
     padding: 25px 20px;
}
#logoutConfirmModal .modal-body i {
    font-size: 24px;
    color: var(--yellow-color);
    margin-bottom: 15px;
    display: block;
}
/* Warning message style for logout confirmation */
.warning-icon-center {
    text-align: center;
    margin-bottom: 15px;
}
.warning-icon {
    color: #f0ad4e;
    font-size: 20px;
}
.warning-message {
    color: var(--text-light);
    font-size: 12px;
    text-align: center;
    font-style: normal;
}

/* Logout Modal Footer Specific */
#logoutConfirmModal .modal-footer {
    display: flex;
    justify-content: space-between;
    padding: 15px 20px;
}

#logoutConfirmModal .modal-footer .btn {
    margin-left: 0;
}


/* Improve form field focus states */
.form-field input:focus, .form-field select:focus, .form-field textarea:focus { box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.25); border-color: var(--primary-color); }

/* Form control class styling */
.form-control { width: 100%; padding: 8px 10px; margin-bottom: 0; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; font-size: 14px; transition: border-color 0.2s ease, box-shadow 0.2s ease; }
.form-control:focus { outline: none; border-color: var(--primary-color); box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2); }

/* Placeholder styling */
::placeholder { color: #aaa; opacity: 1; }
:-ms-input-placeholder { color: #aaa; }
::-ms-input-placeholder { color: #aaa; }

/* Welcome Modal Enhanced Styles */
.welcome-section {
    padding: 0 10px;
}

.welcome-section h3 {
    margin-top: 25px;
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 18px;
    font-weight: 600;
    border-bottom: 1px solid var(--border-light);
    padding-bottom: 8px;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.welcome-section h3 i {
    margin-right: 8px;
    color: var(--primary-color);
    display: inline-block;
    transition: transform 0.3s ease;
}

.welcome-section h3:hover i {
    transform: scale(1.2);
}

.welcome-section .welcome-greeting {
    display: inline-block;
    white-space: nowrap;
    overflow: hidden;
    border-right: 3px solid var(--primary-color);
    animation: typing 1s steps(40, end), blinkCaret 0.75s step-end infinite;
    margin-bottom: 15px;
    font-weight: 600;
    color: var(--secondary-color);
}

.welcome-section p {
    margin-bottom: 15px;
    line-height: 1.6;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.welcome-section ul {
    margin-left: 25px;
    margin-bottom: 20px;
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease, transform 0.4s ease;
}

.welcome-section li {
    margin-bottom: 8px;
    line-height: 1.5;
    position: relative;
    opacity: 0;
    transform: translateX(10px);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.welcome-section li::before {
    content: "•";
    color: var(--primary-color);
    font-weight: bold;
    display: inline-block;
    width: 1em;
    margin-left: -1em;
}

/* Animated Welcome Modal */
#welcomeModal .modal-content {
    overflow: hidden;
}

#welcomeModal .modal-header h2 i {
    animation: pulse 2s infinite;
}

#welcomeModal .modal-footer {
    display: flex;
    justify-content: flex-end;
    padding-top: 20px;
}

#welcomeModal .btn-primary {
    position: relative;
    overflow: hidden;
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: var(--border-radius);
    font-weight: 500;
    transition: background-color 0.3s ease;
}

#welcomeModal .btn-primary:hover {
    background-color: #5a4bd3;
}

#welcomeModal .btn-primary::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

#welcomeModal .btn-primary:hover::after {
    animation: ripple 1s ease-out;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

/* Welcome Section Visible States */
.welcome-section.animated h3,
.welcome-section.animated p,
.welcome-section.animated ul {
    opacity: 1;
    transform: translateY(0);
}

.welcome-section.animated li {
    opacity: 1;
    transform: translateX(0);
}