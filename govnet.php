<?php
// --- PHP Code Block ---
// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}

require_once 'config/database.php'; // Ensure this path is correct
require_once 'access_control.php'; // Include access control functions

// Check if user has full access to this page
$hasFullAccess = hasFullAccess('GovNet');

// Check database connection
if (!$conn) {
    error_log("Database connection failed in govnet.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
} else {
    $db_connection_error = null;
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8
}

// Set the current page name for sidebar highlighting
$current_page_name = 'govnet';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GovNet - Activity Monitoring</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Add page-specific styles here */
    </style>
</head>
<body>
    <!-- Custom Notification Element -->
    <div id="notification" class="notification"> <p class="notification-message" id="notificationMessage"></p> </div>

    <div class="app-container">
        <?php
        // Include the sidebar
        include 'sidebar.php';
        ?>

        <!-- Main Content -->
        <main class="app-main">
            <div class="dashboard-header">
                <div class="office-header">
                    <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                    <div class="office-info"> <h1>DICT SDN - GovNet Activities</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div>
                </div>
            </div>

            <?php if ($db_connection_error): ?>
                <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div>
            <?php endif; ?>

            <!-- Page Content Goes Here -->
            <div class="content-section">
                <h2>Page Under Construction</h2>
                <p>This page is currently being developed. Please check back later.</p>
            </div>
        </main>
    </div>

    <!-- AJAX Endpoint URL -->
    <script> const ajaxHandlerUrl = 'ajax_handler.php'; </script>
    <!-- Core JavaScript -->
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        console.log("GovNet page loaded.");

        // --- Global Elements & Variables ---
        const notificationElement = document.getElementById('notification');
        const notificationMessageElement = document.getElementById('notificationMessage');
        let notificationTimeout;

        // --- Notification Function ---
        function showNotification(message, type = 'info') {
            if (notificationTimeout) clearTimeout(notificationTimeout);
            notificationMessageElement.textContent = message;
            notificationElement.className = `notification ${type}`;
            notificationElement.style.display = 'block';
            notificationTimeout = setTimeout(() => {
                notificationElement.style.display = 'none';
            }, 5000);
        }
      });
    </script>
    <script src="js/settings.js"></script>

    <?php
      // Include common modals
      include 'modals.php';

      // Output access control JavaScript if user has limited access
      echo generateAccessControlJS($hasFullAccess);

      // Close DB connection if it's still open
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>
