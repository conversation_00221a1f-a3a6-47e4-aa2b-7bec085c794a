<?php
// --- PHP Code Block ---
// Start the session at the beginning of the file
session_start();

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header('Location: login.php'); // Redirect to login page if not logged in
    exit;
}

require_once 'config/database.php'; // Ensure this path is correct

// Check database connection
if (!$conn) {
    error_log("Database connection failed in logs.php: " . mysqli_connect_error());
    $db_connection_error = "Error connecting to the database. Please try again later or contact support.";
} else {
    $db_connection_error = null;
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8
}

// Create logs table if it doesn't exist
$create_logs_table_sql = "CREATE TABLE IF NOT EXISTS tbllogs (
    log_id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    username VARCHAR(50) NOT NULL,
    action VARCHAR(255) NOT NULL,
    action_type ENUM('add', 'edit', 'delete', 'upload', 'import', 'export', 'login', 'logout', 'other') NOT NULL,
    item_id INT NULL,
    item_type VARCHAR(50) NULL,
    details TEXT NULL,
    ip_address VARCHAR(45) NULL,
    timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX (user_id),
    INDEX (action_type),
    INDEX (timestamp),
    INDEX (item_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

if (!mysqli_query($conn, $create_logs_table_sql)) {
    error_log("Failed to create logs table: " . mysqli_error($conn));
}

// Pagination setup
$current_page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$results_per_page = isset($_GET['limit']) ? intval($_GET['limit']) : 20;
$offset = ($current_page - 1) * $results_per_page;

// Filtering
$where_clauses = [];
$query_params = [];
$query_param_types = "";

// Date range filter
$start_date = isset($_GET['start_date']) ? trim($_GET['start_date']) : '';
$end_date = isset($_GET['end_date']) ? trim($_GET['end_date']) : '';

if (!empty($start_date)) {
    $where_clauses[] = "timestamp >= ?";
    $query_params[] = $start_date . ' 00:00:00';
    $query_param_types .= 's';
}

if (!empty($end_date)) {
    $where_clauses[] = "timestamp <= ?";
    $query_params[] = $end_date . ' 23:59:59';
    $query_param_types .= 's';
}

// Action type filter
$action_type = isset($_GET['action_type']) ? trim($_GET['action_type']) : '';
if (!empty($action_type)) {
    $where_clauses[] = "action_type = ?";
    $query_params[] = $action_type;
    $query_param_types .= 's';
}

// User filter
$user_filter = isset($_GET['user']) ? trim($_GET['user']) : '';
if (!empty($user_filter)) {
    $where_clauses[] = "(username LIKE ? OR user_id = ?)";
    $query_params[] = '%' . $user_filter . '%';
    $query_params[] = is_numeric($user_filter) ? intval($user_filter) : 0;
    $query_param_types .= 'si';
}

// Search term
$search_term = isset($_GET['search']) ? trim($_GET['search']) : '';
if (!empty($search_term)) {
    $where_clauses[] = "(action LIKE ? OR details LIKE ? OR item_type LIKE ?)";
    $query_params[] = '%' . $search_term . '%';
    $query_params[] = '%' . $search_term . '%';
    $query_params[] = '%' . $search_term . '%';
    $query_param_types .= 'sss';
}

// Build the WHERE clause
$sql_where_clause = !empty($where_clauses) ? " WHERE " . implode(" AND ", $where_clauses) : "";

// Count total logs for pagination
$count_sql = "SELECT COUNT(*) AS total FROM tbllogs" . $sql_where_clause;
$stmt_count = mysqli_prepare($conn, $count_sql);

if ($stmt_count) {
    if (!empty($query_params)) {
        mysqli_stmt_bind_param($stmt_count, $query_param_types, ...$query_params);
    }

    mysqli_stmt_execute($stmt_count);
    $result_count = mysqli_stmt_get_result($stmt_count);
    $row_count = mysqli_fetch_assoc($result_count);
    $total_logs = $row_count['total'];
    mysqli_stmt_close($stmt_count);
} else {
    error_log("Count query preparation failed: " . mysqli_error($conn));
    $total_logs = 0;
}

// Calculate pagination values
$total_pages = ceil($total_logs / $results_per_page);
$current_page = min($current_page, max(1, $total_pages));
$has_previous = $current_page > 1;
$has_next = $current_page < $total_pages;

// Fetch logs
$logs = [];
$logs_fetch_error = null;

if ($total_logs > 0) {
    $logs_sql = "SELECT log_id, user_id, username, action, action_type, item_id, item_type, details, ip_address, timestamp
                 FROM tbllogs" . $sql_where_clause .
                 " ORDER BY timestamp DESC LIMIT ? OFFSET ?";

    $all_params = $query_params;
    $all_params[] = $results_per_page;
    $all_params[] = $offset;
    $all_param_types = $query_param_types . 'ii';

    $stmt_logs = mysqli_prepare($conn, $logs_sql);

    if ($stmt_logs) {
        if (!empty($all_params)) {
            mysqli_stmt_bind_param($stmt_logs, $all_param_types, ...$all_params);
        }

        if (mysqli_stmt_execute($stmt_logs)) {
            $logs_result = mysqli_stmt_get_result($stmt_logs);
            while ($log = mysqli_fetch_assoc($logs_result)) {
                $logs[] = $log;
            }
            mysqli_free_result($logs_result);
        } else {
            $logs_fetch_error = "Error executing logs query: " . mysqli_stmt_error($stmt_logs);
            error_log($logs_fetch_error);
        }

        mysqli_stmt_close($stmt_logs);
    } else {
        $logs_fetch_error = "Error preparing logs query: " . mysqli_error($conn);
        error_log($logs_fetch_error);
    }
}

// Get unique action types for filter dropdown
$action_types = [];
$action_types_sql = "SELECT DISTINCT action_type FROM tbllogs ORDER BY action_type";
$action_types_result = mysqli_query($conn, $action_types_sql);

if ($action_types_result) {
    while ($row = mysqli_fetch_assoc($action_types_result)) {
        $action_types[] = $row['action_type'];
    }
    mysqli_free_result($action_types_result);
}

// Set the current page name for sidebar highlighting
$current_page_name = 'logs';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logs - Activity Monitoring</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Logs page specific styles */
        .content-section {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .logs-filter-container {
            background-color: var(--bg-light);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
            overflow: hidden;
        }

        .logs-filter-form {
            display: flex;
            flex-wrap: nowrap;
            gap: 12px;
            align-items: flex-end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 6px;
            flex: 1;
            min-width: 0; /* Ensures content can shrink below min-content width */
        }

        .filter-group label {
            font-size: 12px;
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 2px;
        }

        .filter-actions {
            display: flex;
            align-items: flex-end;
            gap: 8px;
            justify-content: flex-end;
            flex: 0 0 auto;
        }

        /* Form control styling for filter inputs */
        .logs-filter-container .form-control {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: all 0.2s ease;
            background-color: var(--bg-light);
            width: 100%;
            height: 38px;
            box-sizing: border-box;
        }

        .logs-filter-container .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.1);
        }

        .logs-filter-container select.form-control {
            appearance: none;
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23666%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
            background-repeat: no-repeat;
            background-position: right 12px center;
            background-size: 10px auto;
            padding-right: 30px;
        }

        /* Button styling for filter actions */
        .logs-filter-container .btn-primary {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
            padding: 8px 16px;
            height: 38px;
            min-width: 100px;
            border-radius: var(--border-radius);
        }

        .logs-filter-container .btn-secondary {
            background-color: var(--bg-light);
            color: var(--text-color);
            border: 1px solid var(--border-color);
            padding: 8px 16px;
            height: 38px;
            min-width: 100px;
            border-radius: var(--border-radius);
        }

        /* Action type is now plain text like other columns */
        .timestamp-col { white-space: nowrap; color: var(--text-light); font-size: 12px; }
        .user-col { font-weight: 500; }
        .details-col { max-width: 300px; overflow: hidden; text-overflow: ellipsis; }

        /* Pagination Styling */
        .pagination-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 10px 0;
        }

        .pagination-info {
            color: var(--text-light);
            font-size: 14px;
        }

        .pagination-nav {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .pagination-link {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 4px;
            text-decoration: none;
            color: var(--text-color);
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .pagination-link:hover {
            background-color: #e9e9e9;
        }

        .pagination-link.active {
            background-color: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .pagination-link.disabled {
            color: #aaa;
            pointer-events: none;
            background-color: #f5f5f5;
        }

        .pagination-ellipsis {
            color: var(--text-light);
            padding: 0 5px;
        }

        @media (max-width: 1200px) {
            .logs-filter-form {
                flex-wrap: wrap;
            }

            .filter-group {
                flex: 1 1 calc(20% - 12px);
                min-width: 150px;
            }

            .filter-actions {
                flex: 0 0 auto;
                margin-left: auto;
            }
        }

        @media (max-width: 768px) {
            .logs-filter-form {
                flex-direction: row;
                flex-wrap: wrap;
                gap: 12px;
            }

            .filter-group {
                flex: 1 1 calc(50% - 6px);
            }

            .filter-actions {
                flex: 1 1 100%;
                justify-content: flex-end;
                margin-top: 5px;
            }

            .logs-filter-container .btn-primary,
            .logs-filter-container .btn-secondary {
                flex: 0 1 auto;
                white-space: nowrap;
            }

            .details-col { max-width: 150px; }

            .pagination-controls {
                flex-direction: column;
                gap: 10px;
            }

            .pagination-info {
                text-align: center;
            }
        }
    </style>
</head>
<body>
    <!-- Custom Notification Element -->
    <div id="notification" class="notification"> <p class="notification-message" id="notificationMessage"></p> </div>

    <div class="app-container">
        <?php
        // Include the sidebar
        include 'sidebar.php';
        ?>

        <!-- Main Content -->
        <main class="app-main">
            <div class="dashboard-header">
                <div class="office-header">
                    <div class="office-logo"> <img src="images/dict-logo.png" alt="DICT Logo"> </div>
                    <div class="office-info"> <h1>DICT SDN - System Logs</h1> <p>Ferdinand M. Ortiz St., Brgy. Washington, Surigao City</p> </div>
                </div>
            </div>

            <?php if ($db_connection_error): ?>
                <div class="db-error-message"> <strong>Database Error:</strong> <?php echo htmlspecialchars($db_connection_error); ?> </div>
            <?php endif; ?>

            <!-- Page Content -->
            <div class="content-section">
                <div class="section-header">
                    <div class="section-actions">
                        <div class="results-per-page-form">
                            <label for="resultsPerPage">Show:</label>
                            <div class="select-wrapper">
                                <select id="resultsPerPage" name="limit" onchange="this.form.submit()">
                                    <option value="10" <?php echo $results_per_page == 10 ? 'selected' : ''; ?>>10</option>
                                    <option value="20" <?php echo $results_per_page == 20 ? 'selected' : ''; ?>>20</option>
                                    <option value="50" <?php echo $results_per_page == 50 ? 'selected' : ''; ?>>50</option>
                                    <option value="100" <?php echo $results_per_page == 100 ? 'selected' : ''; ?>>100</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px;"></div> <!-- Added proper spacing to match the image -->

                <!-- Filters -->
                <div class="logs-filter-container">
                    <form class="logs-filter-form" method="GET" action="logs.php">
                        <div class="filter-group">
                            <label for="start_date">From Date:</label>
                            <input type="date" id="start_date" name="start_date" value="<?php echo htmlspecialchars($start_date); ?>" class="form-control">
                        </div>
                        <div class="filter-group">
                            <label for="end_date">To Date:</label>
                            <input type="date" id="end_date" name="end_date" value="<?php echo htmlspecialchars($end_date); ?>" class="form-control">
                        </div>
                        <div class="filter-group">
                            <label for="action_type">Action Type:</label>
                            <select id="action_type" name="action_type" class="form-control">
                                <option value="">All Actions</option>
                                <?php foreach ($action_types as $type): ?>
                                    <option value="<?php echo htmlspecialchars($type); ?>" <?php echo $action_type === $type ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars(ucfirst($type)); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label for="user">User:</label>
                            <input type="text" id="user" name="user" value="<?php echo htmlspecialchars($user_filter); ?>" placeholder="Username or ID" class="form-control">
                        </div>
                        <div class="filter-group">
                            <label for="search">Search:</label>
                            <input type="text" id="search" name="search" value="<?php echo htmlspecialchars($search_term); ?>" placeholder="Search in actions, details..." class="form-control">
                        </div>
                        <div class="filter-actions">
                            <button type="submit" class="btn btn-primary">Apply Filters</button>
                            <a href="logs.php" class="btn btn-secondary">Clear Filters</a>
                            <input type="hidden" name="limit" value="<?php echo $results_per_page; ?>">
                        </div>
                    </form>
                </div>

                <!-- Logs Table -->
                <div class="table-container">
                    <table>
                        <thead>
                            <tr>
                                <th class="col-rownum">#</th>
                                <th>Timestamp</th>
                                <th>User</th>
                                <th>Action Type</th>
                                <th>Action</th>
                                <th>Item Type</th>
                                <th>Item ID</th>
                                <th>Details</th>
                                <th>IP Address</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if ($logs_fetch_error): ?>
                                <tr>
                                    <td colspan="9" class="table-message error">
                                        <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($logs_fetch_error); ?>
                                    </td>
                                </tr>
                            <?php elseif (empty($logs)): ?>
                                <tr>
                                    <td colspan="9" class="table-message">
                                        <i class="fas fa-info-circle"></i> No logs found matching your criteria.
                                    </td>
                                </tr>
                            <?php else: ?>
                                <?php $row_index = 0; ?>
                                <?php foreach ($logs as $log): ?>
                                    <?php $row_number = $offset + $row_index + 1; ?>
                                    <tr>
                                        <td class="col-rownum"><?php echo $row_number; ?></td>
                                        <td class="timestamp-col"><?php echo date('Y-m-d H:i:s', strtotime($log['timestamp'])); ?></td>
                                        <td class="user-col"><?php echo htmlspecialchars($log['username']); ?></td>
                                        <td><?php echo htmlspecialchars(ucfirst($log['action_type'])); ?></td>
                                        <td><?php echo htmlspecialchars($log['action']); ?></td>
                                        <td><?php echo $log['item_type'] ? htmlspecialchars($log['item_type']) : '<span class="text-muted">-</span>'; ?></td>
                                        <td><?php echo $log['item_id'] ? htmlspecialchars($log['item_id']) : '<span class="text-muted">-</span>'; ?></td>
                                        <td class="details-col"><?php echo $log['details'] ? htmlspecialchars($log['details']) : '<span class="text-muted">-</span>'; ?></td>
                                        <td><?php echo $log['ip_address'] ? htmlspecialchars($log['ip_address']) : '<span class="text-muted">-</span>'; ?></td>
                                    </tr>
                                    <?php $row_index++; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($total_logs > 0): ?>
                    <div class="pagination-controls">
                        <div class="pagination-info">
                            Showing <?php echo $offset + 1; ?> - <?php echo min($offset + $results_per_page, $total_logs); ?> of <?php echo $total_logs; ?>
                        </div>
                        <div class="pagination-nav">
                            <?php
                            // Build the query string for pagination links
                            $query_params = $_GET;
                            unset($query_params['page']); // Remove the page parameter
                            $query_string = http_build_query($query_params);
                            $query_string = $query_string ? '&' . $query_string : '';
                            ?>

                            <a href="?page=1<?php echo $query_string; ?>" class="pagination-link <?php echo !$has_previous ? 'disabled' : ''; ?>">Previous</a>

                            <?php
                            // Calculate the range of page numbers to display
                            $start_page = max(1, $current_page - 2);
                            $end_page = min($total_pages, $current_page + 2);

                            // Always show at least 5 pages if available
                            if ($end_page - $start_page + 1 < 5 && $total_pages >= 5) {
                                if ($current_page < 3) {
                                    $end_page = min(5, $total_pages);
                                } else if ($current_page > $total_pages - 2) {
                                    $start_page = max(1, $total_pages - 4);
                                }
                            }

                            // Display page numbers
                            for ($i = $start_page; $i <= $end_page; $i++):
                            ?>
                                <a href="?page=<?php echo $i . $query_string; ?>" class="pagination-link <?php echo $i == $current_page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>

                            <?php if ($end_page < $total_pages): ?>
                                <span class="pagination-ellipsis">...</span>
                                <a href="?page=<?php echo $total_pages . $query_string; ?>" class="pagination-link">
                                    <?php echo $total_pages; ?>
                                </a>
                            <?php endif; ?>

                            <a href="?page=<?php echo min($total_pages, $current_page + 1) . $query_string; ?>" class="pagination-link <?php echo !$has_next ? 'disabled' : ''; ?>">Next</a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>

    <!-- AJAX Endpoint URL -->
    <script> const ajaxHandlerUrl = 'ajax_handler.php'; </script>
    <!-- Core JavaScript -->
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        console.log("Logs page loaded.");

        // --- Global Elements & Variables ---
        const notificationElement = document.getElementById('notification');
        const notificationMessageElement = document.getElementById('notificationMessage');
        let notificationTimeout;

        // --- Notification Function ---
        function showNotification(message, type = 'info') {
            if (notificationTimeout) clearTimeout(notificationTimeout);
            notificationMessageElement.textContent = message;
            notificationElement.className = `notification ${type}`;
            notificationElement.style.display = 'block';
            notificationTimeout = setTimeout(() => {
                notificationElement.style.display = 'none';
            }, 5000);
        }

        // --- Results Per Page Handling ---
        const resultsPerPageSelect = document.getElementById('resultsPerPage');
        if (resultsPerPageSelect) {
            resultsPerPageSelect.addEventListener('change', function() {
                // Get current URL and parameters
                const url = new URL(window.location.href);
                // Update or add the limit parameter
                url.searchParams.set('limit', this.value);
                // Reset to first page when changing results per page
                url.searchParams.set('page', '1');
                // Navigate to the new URL
                window.location.href = url.toString();
            });
        }

        // --- Date Range Validation ---
        const startDateInput = document.getElementById('start_date');
        const endDateInput = document.getElementById('end_date');
        const filterForm = document.querySelector('.logs-filter-form');

        if (filterForm && startDateInput && endDateInput) {
            filterForm.addEventListener('submit', function(e) {
                const startDate = startDateInput.value ? new Date(startDateInput.value) : null;
                const endDate = endDateInput.value ? new Date(endDateInput.value) : null;

                if (startDate && endDate && startDate > endDate) {
                    e.preventDefault();
                    showNotification('Start date cannot be after end date', 'error');
                    startDateInput.focus();
                }
            });
        }

        // --- Table Row Highlighting ---
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseover', function() {
                this.style.backgroundColor = 'var(--hover-color, #f5f5f5)';
            });

            row.addEventListener('mouseout', function() {
                this.style.backgroundColor = '';
            });
        });

        // --- Details Expansion ---
        const detailsCells = document.querySelectorAll('.details-col');
        detailsCells.forEach(cell => {
            if (cell.textContent.length > 50) {
                const originalText = cell.textContent;
                const truncatedText = originalText.substring(0, 50) + '...';

                cell.setAttribute('data-full-text', originalText);
                cell.setAttribute('data-truncated', 'true');
                cell.innerHTML = truncatedText + ' <a href="#" class="expand-details">[+]</a>';

                const expandLink = cell.querySelector('.expand-details');
                expandLink.addEventListener('click', function(e) {
                    e.preventDefault();

                    if (cell.getAttribute('data-truncated') === 'true') {
                        cell.innerHTML = cell.getAttribute('data-full-text') + ' <a href="#" class="expand-details">[-]</a>';
                        cell.setAttribute('data-truncated', 'false');
                    } else {
                        cell.innerHTML = truncatedText + ' <a href="#" class="expand-details">[+]</a>';
                        cell.setAttribute('data-truncated', 'true');
                    }

                    // Reattach event listener to the new link
                    cell.querySelector('.expand-details').addEventListener('click', arguments.callee);
                });
            }
        });
      });
    </script>
    <script src="js/settings.js"></script>

    <?php
      // Include common modals
      include 'modals.php';

      // Close DB connection if it's still open
      if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
          mysqli_close($conn);
      }
    ?>
</body>
</html>
