<?php
/**
 * Log Helper Functions
 *
 * This file contains helper functions for logging user actions in the system.
 */

/**
 * Add a log entry to the system logs
 *
 * @param int $user_id The ID of the user performing the action
 * @param string $username The username of the user performing the action
 * @param string $action The action description
 * @param string $action_type The type of action (add, edit, delete, upload, import, export, login, logout, other)
 * @param int|null $item_id The ID of the item being acted upon (optional)
 * @param string|null $item_type The type of item being acted upon (optional)
 * @param string|null $details Additional details about the action (optional)
 * @return bool True if the log was added successfully, false otherwise
 */
function add_log_entry($user_id, $username, $action, $action_type, $item_id = null, $item_type = null, $details = null) {
    global $conn;

    // Ensure database connection exists
    if (!$conn || !@mysqli_ping($conn)) {
        error_log("Log Helper: DB connection lost before logging action: " . $action);
        require_once __DIR__ . '/config/database.php'; // Attempt to reconnect
        if (!$conn || !@mysqli_ping($conn)) {
            error_log("Log Helper: Failed to reconnect to DB for logging action: " . $action);
            return false;
        }
        mysqli_set_charset($conn, "utf8mb4");
    }

    // Create logs table if it doesn't exist
    $create_logs_table_sql = "CREATE TABLE IF NOT EXISTS tbllogs (
        log_id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        username VARCHAR(50) NOT NULL,
        action VARCHAR(255) NOT NULL,
        action_type ENUM('add', 'edit', 'delete', 'upload', 'import', 'export', 'login', 'logout', 'other') NOT NULL,
        item_id INT NULL,
        item_type VARCHAR(50) NULL,
        details TEXT NULL,
        ip_address VARCHAR(45) NULL,
        timestamp DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
        INDEX (user_id),
        INDEX (action_type),
        INDEX (timestamp),
        INDEX (item_type)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;";

    if (!mysqli_query($conn, $create_logs_table_sql)) {
        error_log("Log Helper: Failed to create logs table: " . mysqli_error($conn));
        return false;
    }

    // Get client IP address
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;

    // Validate action_type
    $valid_action_types = ['add', 'edit', 'delete', 'upload', 'import', 'export', 'login', 'logout', 'other'];
    if (!in_array($action_type, $valid_action_types)) {
        $action_type = 'other';
    }

    // Prepare and execute the insert query
    $sql = "INSERT INTO tbllogs (user_id, username, action, action_type, item_id, item_type, details, ip_address)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

    $stmt = mysqli_prepare($conn, $sql);
    if (!$stmt) {
        error_log("Log Helper: DB prepare error: " . mysqli_error($conn));
        return false;
    }

    mysqli_stmt_bind_param($stmt, "isssssss", $user_id, $username, $action, $action_type, $item_id, $item_type, $details, $ip_address);

    $success = mysqli_stmt_execute($stmt);
    if (!$success) {
        error_log("Log Helper: DB execute error: " . mysqli_stmt_error($stmt));
    }

    mysqli_stmt_close($stmt);
    return $success;
}
