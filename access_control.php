<?php
/**
 * Access Control Helper Functions
 *
 * This file contains functions to check if a user has full access to a specific bureau's page
 * or if they should have limited access (view/export only).
 */

/**
 * Check if the current user has full access to the specified bureau
 *
 * @param string $bureau The bureau to check access for
 * @return bool True if user has full access, false otherwise
 */
function hasFullAccess($bureau) {
    // Admin has full access to all bureaus
    if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin') {
        return true;
    }

    // Check if user's bureau matches the requested bureau
    if (isset($_SESSION['bureau']) && $_SESSION['bureau'] === $bureau) {
        return true;
    }

    // Default to limited access
    return false;
}

/**
 * Get the current page's bureau based on the filename
 *
 * @return string The bureau name for the current page
 */
function getCurrentPageBureau() {
    $script_name = basename($_SERVER['SCRIPT_NAME']);

    switch ($script_name) {
        case 'cybersecurity.php':
            return 'Cybersecurity';
        case 'elgu.php':
            return 'eLGU BPLS';
        case 'freewifi4all.php':
            return 'FreeWifi4All';
        case 'govnet.php':
            return 'GovNet';
        case 'iidb.php':
            return 'IIDB';
        case 'ilcdb.php':
            return 'ILCDB';
        case 'gecs.php':
            return 'GECS';
        case 'inventory.php':
            return 'Inventory Items';
        default:
            return '';
    }
}

/**
 * Check if the current user has full access to the current page
 *
 * @return bool True if user has full access, false otherwise
 */
function hasFullAccessToCurrentPage() {
    $bureau = getCurrentPageBureau();
    return hasFullAccess($bureau);
}

/**
 * Generate JavaScript code to implement access control on the client side
 *
 * @param bool $hasFullAccess Whether the user has full access to the current page
 * @return string JavaScript code to implement access control
 */
function generateAccessControlJS($hasFullAccess) {
    if ($hasFullAccess) {
        // No restrictions needed
        return '';
    }

    // JavaScript to disable edit, delete, upload, view files, add item, and import functionality
    return <<<JS
<script>
// Access control script - Limited access mode
document.addEventListener('DOMContentLoaded', function() {
    // Hide or disable buttons for limited access
    const limitedAccessElements = [
        // Edit button
        { selector: '#editButton', action: 'disable' },
        // Delete button
        { selector: '#deleteButton', action: 'disable' },
        // Upload button
        { selector: '#uploadButton', action: 'disable' },
        // View files button
        { selector: '#viewFilesButton', action: 'disable' },
        // Add activity button
        { selector: '#addActivityButton', action: 'disable' },
        // Add participant button
        { selector: '#addParticipantButton', action: 'disable' },
        // Add letter button (for FreeWifi4All)
        { selector: '#addLetterButton', action: 'disable' },
        // Add LGU button (for FreeWifi4All)
        { selector: '#addLGUButton', action: 'disable' },
        // Add Tech4ED button (for ILCDB)
        { selector: '#addTech4EDButton', action: 'disable' },
        // Import button
        { selector: '#importButton', action: 'disable' },
        // Manage targets button
        { selector: '#manageTargetsButton', action: 'disable' }
    ];

    // Apply restrictions
    limitedAccessElements.forEach(item => {
        const element = document.querySelector(item.selector);
        if (element) {
            if (item.action === 'disable') {
                element.disabled = true;
                element.classList.add('disabled');
                element.title = 'You do not have permission to perform this action';
            } else if (item.action === 'hide') {
                element.style.display = 'none';
            }
        }
    });

    // Hide all checkboxes to prevent selection
    document.querySelectorAll('.row-checkbox, .select-all-checkbox').forEach(checkbox => {
        checkbox.style.display = 'none';
    });

    // Add a notice at the top of the page
    const mainContent = document.querySelector('.app-main');
    if (mainContent) {
        const noticeDiv = document.createElement('div');
        noticeDiv.className = 'limited-access-notice';
        noticeDiv.innerHTML = '<i class="fas fa-info-circle"></i> You have limited access to this page. You can view and export data, but cannot edit, delete, upload files, or add new items.';
        noticeDiv.style.backgroundColor = '#f8f9fa';
        noticeDiv.style.border = '1px solid #ddd';
        noticeDiv.style.borderRadius = '4px';
        noticeDiv.style.padding = '10px 15px';
        noticeDiv.style.marginBottom = '15px';
        noticeDiv.style.color = '#495057';

        // Insert after the header
        const header = mainContent.querySelector('.dashboard-header');
        if (header && header.nextSibling) {
            mainContent.insertBefore(noticeDiv, header.nextSibling);
        } else {
            mainContent.insertBefore(noticeDiv, mainContent.firstChild);
        }
    }
});
</script>
JS;
}
?>
