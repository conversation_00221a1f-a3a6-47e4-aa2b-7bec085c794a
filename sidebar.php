<?php
/**
 * Reusable sidebar component for the DICT SDN Office application
 *
 * This file contains the sidebar HTML structure that can be included in all pages.
 * It highlights the active page based on the $current_page_name variable.
 *
 * Usage:
 * 1. Set $current_page_name in your page before including this file
 * 2. Include this file using: include 'sidebar.php';
 */

// Default to 'index' if $current_page_name is not set
if (!isset($current_page_name)) {
    $current_page_name = 'index';
}

// Get user information from session
// Only use fallback values if user is not logged in
if (isset($_SESSION['user_id'])) {
    $username = $_SESSION['username'] ?? 'ILCDB';
    $full_name = $_SESSION['full_name'] ?? 'DICT SDN Office';
} else {
    $username = 'ILCDB';
    $full_name = 'DICT SDN Office';
}
?>

<!-- ========== App Sidebar ========== -->
<aside class="app-sidebar">
    <div class="sidebar-header">
        <div class="app-logo"><i class="fas fa-cube"></i></div>
        <div class="app-title"><strong><?php echo htmlspecialchars($full_name); ?></strong><span><?php echo htmlspecialchars($username); ?></span></div>
    </div>
    <nav class="sidebar-nav">
        <ul>
            <li><a href="index.php" class="<?php echo ($current_page_name === 'index') ? 'active' : ''; ?>"><i class="fas fa-th-large fa-fw"></i> <span>Dashboard</span></a></li>
            <li><a href="cybersecurity.php" class="<?php echo ($current_page_name === 'cybersecurity') ? 'active' : ''; ?>"><i class="fas fa-shield-alt fa-fw"></i> <span>Cybersecurity</span></a></li>
            <li><a href="elgu.php" class="<?php echo ($current_page_name === 'elgu') ? 'active' : ''; ?>"><i class="fas fa-network-wired fa-fw"></i> <span>eLGU BPLS</span></a></li>
            <li><a href="freewifi4all.php" class="<?php echo ($current_page_name === 'freewifi4all') ? 'active' : ''; ?>"><i class="fas fa-wifi fa-fw"></i> <span>FreeWifi4All</span></a></li>
            <li><a href="iidb.php" class="<?php echo ($current_page_name === 'iidb') ? 'active' : ''; ?>"><i class="fas fa-database fa-fw"></i> <span>IIDB</span></a></li>
            <li><a href="ilcdb.php" class="<?php echo ($current_page_name === 'ilcdb') ? 'active' : ''; ?>"><i class="fas fa-desktop fa-fw"></i> <span>ILCDB</span></a></li>
            <li><a href="gecs.php" class="<?php echo ($current_page_name === 'gecs') ? 'active' : ''; ?>"><i class="fas fa-file-alt fa-fw"></i> <span>GECS</span></a></li>
            <li><a href="inventory.php" class="<?php echo ($current_page_name === 'inventory') ? 'active' : ''; ?>"><i class="fas fa-shopping-cart fa-fw"></i> <span>Inventory Items</span></a></li>
        </ul>
    </nav>
    <nav class="sidebar-footer-nav">
        <ul>
            <li><a href="logs.php" class="<?php echo ($current_page_name === 'logs') ? 'active' : ''; ?>"><i class="fas fa-history fa-fw"></i> <span>Logs</span></a></li>
            <?php if (isset($_SESSION['role']) && $_SESSION['role'] === 'admin'): ?>
            <li><a href="credentials.php" class="<?php echo ($current_page_name === 'credentials') ? 'active' : ''; ?>"><i class="fas fa-key fa-fw"></i> <span>Credentials</span></a></li>
            <?php endif; ?>
            <li>
                <a href="#" id="settingsBtn" role="button" aria-haspopup="true" aria-expanded="false">
                    <i class="fas fa-cog fa-fw"></i> <span>Settings</span>
                </a>
                <div id="settingsMenuPopup" class="settings-popup">
                    <a href="#" id="updateProfileBtn"><i class="fas fa-user-edit fa-fw"></i> Update Profile</a>
                    <a href="#" id="logoutBtn"><i class="fas fa-sign-out-alt fa-fw"></i> Log Out</a>
                </div>
            </li>
        </ul>
    </nav>
</aside>
