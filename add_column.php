<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "dict_sdn_db";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Add target_participants column if it doesn't exist
$result = $conn->query("SHOW COLUMNS FROM targets LIKE 'target_participants'");
if ($result->num_rows == 0) {
    echo "Adding target_participants column...\n";
    $sql = "ALTER TABLE targets ADD COLUMN target_participants INT NULL AFTER target_activities";
    if ($conn->query($sql) === TRUE) {
        echo "Column added successfully.\n";
    } else {
        echo "Error adding column: " . $conn->error . "\n";
    }
} else {
    echo "Column 'target_participants' already exists.\n";
}

// Close connection
$conn->close();
?>
