<?php
// logout.php
session_start(); // Access the existing session

// Include required files for logging
require_once 'config/database.php';
require_once 'log_helper.php';

// Log the logout action if user is logged in
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    $user_id = $_SESSION['user_id'];
    $username = $_SESSION['username'];

    // Log the logout action
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
    $details = "Logout from IP: " . $ip;
    add_log_entry(
        $user_id,
        $username,
        "User logged out",
        "logout",
        null,
        "user",
        $details
    );
}

// Unset all session variables
$_SESSION = array();

// If using session cookies, destroy the cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}

// Finally, destroy the session.
session_destroy();

// Redirect to the login page
header("Location: login.php"); // Adjust the path to your login page if needed
exit; // Ensure no further code execution
?>