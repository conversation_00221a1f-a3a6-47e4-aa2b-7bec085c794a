/* Filter Dropdown Styles */
.filter-dropdown-container {
    background-color: var(--bg-light);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 15px;
    margin-bottom: 20px;
    display: block; /* Always visible by default */
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

.filter-group {
    flex: 1 1 200px;
    min-width: 0;
}

.filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 13px;
    color: var(--secondary-color);
}

.filter-group select,
.filter-group input {
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--bg-light);
}

.filter-group select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%23666%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E');
    background-repeat: no-repeat;
    background-position: right 10px top 50%;
    background-size: 10px auto;
    padding-right: 30px;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(106, 90, 224, 0.2);
}

.filter-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
    flex-shrink: 0;
}

/* Apply Filters button (primary) */
.filter-actions .btn-primary {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transition: none;
}

/* Clear Filters button (secondary) */
.filter-actions .btn-secondary {
    background-color: #f8f9fa;
    color: #495057;
    border-color: #dee2e6;
    transition: none;
}

.filter-toggle-button {
    display: none; /* Hide the filter toggle button */
}

/* New integrated filter icon */
.filter-toggle-icon {
    display: none; /* Hide the filter toggle icon */
}

/* Update search bar to accommodate the filter icon */
.search-bar {
    display: flex;
    align-items: center;
    background-color: var(--bg-light);
    padding: 8px 12px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-form {
        flex-direction: column;
        gap: 10px;
    }

    .filter-group {
        flex-basis: 100%;
    }

    .filter-actions {
        margin-left: 0;
        width: 100%;
        justify-content: space-between;
        margin-top: 10px;
    }

    .filter-actions .btn {
        flex: 1;
    }
}
