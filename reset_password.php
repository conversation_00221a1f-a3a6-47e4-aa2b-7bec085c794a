<?php
session_start(); // Start the session at the very beginning

// If already logged in, redirect to dashboard
if (isset($_SESSION['user_id'])) {
    header("Location: index.php");
    exit();
}

ini_set('display_errors', 0); // Don't display errors directly to users
ini_set('log_errors', 1);    // Log errors
error_reporting(E_ALL);

require_once 'config/database.php'; // Include database configuration
require_once 'log_helper.php'; // Include log helper functions

// Initialize variables
$error_message = '';
$success_message = '';
$token = '';
$user_data = null;

// Check database connection
if (!$conn) {
    error_log("Database connection failed in reset_password.php: " . mysqli_connect_error());
    $error_message = "System error. Please try again later.";
} else {
    mysqli_set_charset($conn, "utf8mb4"); // Ensure UTF-8

    // Handle AJAX token validation request
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'validate_token') {
        $reset_token = isset($_POST['reset_token']) ? trim($_POST['reset_token']) : '';

        // Validate token format (basic validation)
        if (empty($reset_token)) {
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'Reset token is required.']);
            exit();
        }

        // Check token in database
        $sql = "SELECT user_id, username, full_name, email FROM tblusers WHERE reset_password = ? LIMIT 1";
        $stmt = mysqli_prepare($conn, $sql);

        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $reset_token);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if ($result && mysqli_num_rows($result) === 1) {
                // Token is valid
                header('Content-Type: application/json');
                echo json_encode(['success' => true, 'message' => 'Token validated successfully.']);
                exit();
            } else {
                // Token is invalid
                header('Content-Type: application/json');
                echo json_encode(['success' => false, 'message' => 'Invalid reset token. Please contact your administrator.']);
                exit();
            }

            mysqli_stmt_close($stmt);
        } else {
            // Database error
            error_log("Reset token validation statement preparation failed: " . mysqli_error($conn));
            header('Content-Type: application/json');
            echo json_encode(['success' => false, 'message' => 'System error. Please try again later.']);
            exit();
        }
    }

    // Handle GET request with token parameter
    if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['token'])) {
        $token = trim($_GET['token']);

        // Validate token in database
        $sql = "SELECT user_id, username, full_name, email FROM tblusers WHERE reset_password = ? LIMIT 1";
        $stmt = mysqli_prepare($conn, $sql);

        if ($stmt) {
            mysqli_stmt_bind_param($stmt, "s", $token);
            mysqli_stmt_execute($stmt);
            $result = mysqli_stmt_get_result($stmt);

            if ($result && mysqli_num_rows($result) === 1) {
                // Token is valid, get user data
                $user_data = mysqli_fetch_assoc($result);
            } else {
                // Token is invalid
                $error_message = "Invalid or expired reset token. Please try again or contact your administrator.";
                $token = ''; // Clear token
            }

            mysqli_stmt_close($stmt);
        } else {
            // Database error
            error_log("Reset token validation statement preparation failed: " . mysqli_error($conn));
            $error_message = "System error. Please try again later.";
        }
    }

    // Handle password reset form submission
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'reset_password') {
        $reset_token = isset($_POST['token']) ? trim($_POST['token']) : '';
        $new_password = isset($_POST['new_password']) ? $_POST['new_password'] : '';
        $confirm_password = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

        // Validate inputs
        if (empty($reset_token)) {
            $error_message = "Invalid request. Missing reset token.";
        } elseif (empty($new_password)) {
            $error_message = "Please enter a new password.";
        } elseif (strlen($new_password) < 8) {
            $error_message = "Password must be at least 8 characters long.";
        } elseif ($new_password !== $confirm_password) {
            $error_message = "Passwords do not match.";
        } else {
            // Validate token and update password
            $sql = "SELECT user_id, username FROM tblusers WHERE reset_password = ? LIMIT 1";
            $stmt = mysqli_prepare($conn, $sql);

            if ($stmt) {
                mysqli_stmt_bind_param($stmt, "s", $reset_token);
                mysqli_stmt_execute($stmt);
                $result = mysqli_stmt_get_result($stmt);

                if ($result && mysqli_num_rows($result) === 1) {
                    $user = mysqli_fetch_assoc($result);
                    $user_id = $user['user_id'];
                    $username = $user['username'];

                    // Hash the new password
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                    // Update password but keep the reset token (as requested)
                    $update_sql = "UPDATE tblusers SET password = ?, updated_at = NOW() WHERE user_id = ?";
                    $update_stmt = mysqli_prepare($conn, $update_sql);

                    if ($update_stmt) {
                        mysqli_stmt_bind_param($update_stmt, "si", $hashed_password, $user_id);

                        if (mysqli_stmt_execute($update_stmt)) {
                            // Password updated successfully
                            $success_message = "Password reset successful. You can now log in with your new password.";

                            // Log the password reset
                            $ip = $_SERVER['REMOTE_ADDR'] ?? 'Unknown';
                            $details = "Password reset from IP: " . $ip;
                            add_log_entry(
                                $user_id,
                                $username,
                                "User reset password",
                                "other",
                                null,
                                "user",
                                $details
                            );

                            // Clear token
                            $token = '';
                        } else {
                            error_log("Failed to update password for user_id: " . $user_id . " - Error: " . mysqli_stmt_error($update_stmt));
                            $error_message = "Failed to update password. Please try again later.";
                        }

                        mysqli_stmt_close($update_stmt);
                    } else {
                        error_log("Failed to prepare password update statement: " . mysqli_error($conn));
                        $error_message = "System error. Please try again later.";
                    }
                } else {
                    $error_message = "Invalid or expired reset token. Please try again or contact your administrator.";
                }

                mysqli_stmt_close($stmt);
            } else {
                error_log("Reset password statement preparation failed: " . mysqli_error($conn));
                $error_message = "System error. Please try again later.";
            }
        }
    }
}

// Close DB connection if it was opened
if (isset($conn) && is_object($conn) && @mysqli_ping($conn)) {
    mysqli_close($conn);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - Activity Monitoring</title>
    <!-- Link to your existing stylesheet -->
    <link rel="stylesheet" href="css/style.css">
    <!-- Link to Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Inherit variables from style.css */
        :root {
             /* Define fallback variables ONLY if style.css might not load,
                otherwise rely on the main stylesheet. */
            --bg-color: #F7F8FC;
            --bg-light: #FFFFFF;
            --border-color: #EAEAEA;
            --border-radius: 8px;
            --primary-color: #6A5AE0;
            --secondary-color: #4A4A6A;
            --text-light: #9a9a9a;
            --red-color: #E57373;
            --green-color: #66BB6A;
            --box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            --font-family: 'Poppins', sans-serif;
        }

        /* Login specific styles */
        body.reset-password-page {
            /* Modern, clean background gradient */
            background: linear-gradient(135deg, #f0f4f8 0%, #e1e8f0 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: var(--font-family);
            padding: 20px; /* Add padding for small screens */
        }

        .reset-container {
            background-color: var(--bg-light);
            padding: 40px 35px;
            border-radius: var(--border-radius);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid var(--border-color);
            width: 100%;
            max-width: 420px;
            text-align: center;
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .reset-logo {
            margin-bottom: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .reset-logo img {
            height: 70px;
            width: auto;
        }

        .reset-title {
            font-size: 19px;
            font-weight: 600;
            color: var(--secondary-color);
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .reset-subtitle {
            font-size: 14px;
            color: var(--text-light);
            margin-bottom: 30px;
        }

        .reset-form .form-group {
            margin-bottom: 22px;
            text-align: left;
        }

        .reset-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 14px;
            color: var(--secondary-color);
        }

        /* Style for the input group (icon + input) */
        .input-group {
            position: relative;
        }

        .input-group .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            pointer-events: none;
            font-size: 16px;
            transition: color 0.2s ease;
        }

        .input-group .input-icon-right {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            pointer-events: none;
            font-size: 16px;
            transition: color 0.2s ease;
        }

        /* Password toggle button */
        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-light);
            cursor: pointer;
            font-size: 16px;
            transition: color 0.2s ease;
            background: none;
            border: none;
            padding: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }

        .password-toggle:hover {
            color: var(--primary-color);
        }

        /* Change icon color on input focus for visual feedback */
        .reset-form .input-group input:focus + .input-icon {
            color: var(--primary-color);
        }

        .reset-form .input-group input[type="password"],
        .reset-form .input-group input[type="text"] {
            width: 100%;
            padding: 12px 15px 12px 45px;
            padding-right: 45px; /* Add right padding for the toggle button */
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
            background-color: #fdfdfd;
            font-family: var(--font-family);
        }

        .reset-form .input-group input[type="password"]:focus,
        .reset-form .input-group input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(106, 90, 224, 0.15);
            background-color: var(--bg-light);
        }

        /* Ensure consistent styling when toggling password visibility */
        .reset-form .input-group input {
            font-size: 14px !important;
            font-family: var(--font-family) !important;
        }

        /* Style placeholder text */
        .reset-form input::placeholder {
            color: #b0b0b0;
            opacity: 1;
        }

        .reset-button {
            width: 100%;
            padding: 13px 15px;
            font-size: 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: var(--border-radius);
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
            font-weight: 600;
            margin-top: 15px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .reset-button:hover {
            background-color: #5a4bd3;
        }

        .reset-button:active {
            transform: translateY(1px);
        }

        .error-message {
            background-color: rgba(229, 115, 115, 0.1);
            color: var(--red-color);
            border: 1px solid rgba(229, 115, 115, 0.3);
            border-radius: var(--border-radius);
            padding: 12px 15px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
            display: <?php echo empty($error_message) ? 'none' : 'block'; ?>;
            animation: shake 0.4s ease-in-out;
        }

        .success-message {
            background-color: rgba(102, 187, 106, 0.1);
            color: var(--green-color);
            border: 1px solid rgba(102, 187, 106, 0.3);
            border-radius: var(--border-radius);
            padding: 12px 15px;
            margin-bottom: 20px;
            font-size: 14px;
            text-align: center;
            display: <?php echo empty($success_message) ? 'none' : 'block'; ?>;
        }

        .message-icon {
            margin-right: 8px;
        }

        /* Simple shake animation */
        @keyframes shake {
          10%, 90% { transform: translateX(-1px); }
          20%, 80% { transform: translateX(2px); }
          30%, 50%, 70% { transform: translateX(-3px); }
          40%, 60% { transform: translateX(3px); }
        }

        .reset-footer {
            margin-top: 30px;
            font-size: 13px;
            color: var(--text-light);
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }

        .reset-footer a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .reset-footer a:hover {
            text-decoration: underline;
        }

        .reset-footer .copyright {
            text-align: center;
        }

        /* Password strength indicator */
        .password-strength {
            margin-top: 8px;
            font-size: 12px;
            display: flex;
            align-items: center;
        }

        .password-strength.weak {
            color: #e57373;
        }

        .password-strength.medium {
            color: #ffb74d;
        }

        .password-strength.strong {
            color: #81c784;
        }

        .password-strength i {
            margin-right: 5px;
        }

        /* Responsive Adjustments */
        @media (max-width: 480px) {
            .reset-container {
                padding: 30px 25px;
                max-width: 90%;
            }
            .reset-logo img {
                height: 60px;
            }
            .reset-title {
                font-size: 18px;
            }
            .reset-subtitle {
                margin-bottom: 25px;
            }
            .reset-form .input-group input[type="password"] {
                padding: 11px 15px 11px 40px;
                padding-right: 40px;
            }
            .input-group .input-icon {
                font-size: 15px;
                left: 13px;
            }

            .input-group .input-icon-right {
                font-size: 15px;
                right: 13px;
            }
            .password-toggle {
                right: 13px;
                font-size: 15px;
            }
            .reset-button {
                padding: 12px 15px;
            }
        }
    </style>
</head>
<body class="reset-password-page">

    <div class="reset-container">
        <div class="reset-logo">
            <img src="images/dict-logo.png" alt="DICT Logo">
        </div>

        <h2 class="reset-title">DICT SDN Provincial Office</h2>
        <p class="reset-subtitle">Reset Your Password</p>

        <!-- Error Message Display -->
        <div class="error-message" id="errorMessage">
            <?php if (!empty($error_message)): ?>
                <i class="fas fa-exclamation-triangle message-icon"></i> <?php echo htmlspecialchars($error_message); ?>
            <?php endif; ?>
        </div>

        <!-- Success Message Display -->
        <div class="success-message" id="successMessage">
            <?php if (!empty($success_message)): ?>
                <i class="fas fa-check-circle message-icon"></i> <?php echo htmlspecialchars($success_message); ?>
            <?php endif; ?>
        </div>

        <?php if (!empty($token) && $user_data): ?>
            <!-- Password Reset Form -->
            <form action="reset_password.php" method="post" class="reset-form" id="resetPasswordForm" novalidate>
                <input type="hidden" name="action" value="reset_password">
                <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">

                <div class="form-group">
                    <label for="new_password">New Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="new_password" name="new_password" placeholder="Enter your new password" required>
                        <button type="button" class="password-toggle" id="newPasswordToggle" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" id="newPasswordToggleIcon"></i>
                        </button>
                    </div>
                    <div id="password_strength" class="password-strength"></div>
                </div>

                <div class="form-group">
                    <label for="confirm_password">Confirm Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm your new password" required>
                        <button type="button" class="password-toggle" id="confirmPasswordToggle" aria-label="Toggle password visibility">
                            <i class="fas fa-eye" id="confirmPasswordToggleIcon"></i>
                        </button>
                    </div>
                </div>

                <button type="submit" class="reset-button">
                    <i class="fas fa-save"></i> Reset Password
                </button>
            </form>
        <?php elseif (!empty($success_message)): ?>
            <!-- Success message with login link -->
            <div class="reset-success">
                <a href="login.php" class="reset-button">
                    <i class="fas fa-sign-in-alt"></i> Go to Login
                </a>
            </div>
        <?php else: ?>
            <!-- Invalid or missing token message with back link -->
            <div class="reset-error">
                <p>Please provide a valid reset token to continue.</p>
                <a href="login.php" class="reset-button">
                    <i class="fas fa-arrow-left"></i> Back to Login
                </a>
            </div>
        <?php endif; ?>

        <div class="reset-footer">
            <a href="login.php">Back to Login</a>
            <div class="copyright">© <?php echo date("Y"); ?> DICT Surigao del Norte. All rights reserved.</div>
        </div>
    </div>

    <!-- Password Toggle and Validation Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Password Toggle Functionality
            function setupPasswordToggle(passwordId, toggleId, toggleIconId) {
                const passwordInput = document.getElementById(passwordId);
                const passwordToggle = document.getElementById(toggleId);
                const passwordToggleIcon = document.getElementById(toggleIconId);

                if (passwordToggle && passwordInput && passwordToggleIcon) {
                    // Store original styles
                    const originalStyles = {
                        borderColor: window.getComputedStyle(passwordInput).borderColor,
                        boxShadow: window.getComputedStyle(passwordInput).boxShadow,
                        backgroundColor: window.getComputedStyle(passwordInput).backgroundColor,
                        padding: window.getComputedStyle(passwordInput).padding,
                        fontSize: window.getComputedStyle(passwordInput).fontSize,
                        fontFamily: window.getComputedStyle(passwordInput).fontFamily
                    };

                    passwordToggle.addEventListener('click', function() {
                        // Toggle password visibility
                        if (passwordInput.type === 'password') {
                            // Create a text input that looks identical
                            passwordInput.type = 'text';
                            passwordToggleIcon.classList.remove('fa-eye');
                            passwordToggleIcon.classList.add('fa-eye-slash');
                        } else {
                            // Switch back to password
                            passwordInput.type = 'password';
                            passwordToggleIcon.classList.remove('fa-eye-slash');
                            passwordToggleIcon.classList.add('fa-eye');
                        }

                        // Restore original styles to ensure consistency
                        passwordInput.style.borderColor = originalStyles.borderColor;
                        passwordInput.style.boxShadow = originalStyles.boxShadow;
                        passwordInput.style.backgroundColor = originalStyles.backgroundColor;
                        passwordInput.style.padding = originalStyles.padding;
                        passwordInput.style.fontSize = originalStyles.fontSize;
                        passwordInput.style.fontFamily = originalStyles.fontFamily;

                        // Focus back on the input
                        passwordInput.focus();
                    });

                    // Prevent form submission when clicking the toggle button
                    passwordToggle.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                    });
                }
            }

            // Setup password toggles
            setupPasswordToggle('new_password', 'newPasswordToggle', 'newPasswordToggleIcon');
            setupPasswordToggle('confirm_password', 'confirmPasswordToggle', 'confirmPasswordToggleIcon');

            // Password Strength Indicator
            const newPasswordInput = document.getElementById('new_password');
            const passwordStrength = document.getElementById('password_strength');

            if (newPasswordInput && passwordStrength) {
                newPasswordInput.addEventListener('input', function() {
                    const password = this.value;
                    let strength = 0;
                    let message = '';

                    if (password.length >= 8) strength += 1;
                    if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 1;
                    if (password.match(/\d/)) strength += 1;
                    if (password.match(/[^a-zA-Z\d]/)) strength += 1;

                    switch (strength) {
                        case 0:
                        case 1:
                            message = '<i class="fas fa-exclamation-circle"></i> Weak password';
                            passwordStrength.className = 'password-strength weak';
                            break;
                        case 2:
                        case 3:
                            message = '<i class="fas fa-info-circle"></i> Medium strength password';
                            passwordStrength.className = 'password-strength medium';
                            break;
                        case 4:
                            message = '<i class="fas fa-check-circle"></i> Strong password';
                            passwordStrength.className = 'password-strength strong';
                            break;
                    }

                    passwordStrength.innerHTML = message;
                });
            }

            // Form Validation
            const resetPasswordForm = document.getElementById('resetPasswordForm');
            const confirmPasswordInput = document.getElementById('confirm_password');
            const errorMessage = document.getElementById('errorMessage');

            if (resetPasswordForm) {
                resetPasswordForm.addEventListener('submit', function(e) {
                    let isValid = true;
                    let errorText = '';

                    // Check password length
                    if (newPasswordInput && newPasswordInput.value.length < 8) {
                        isValid = false;
                        errorText = 'Password must be at least 8 characters long.';
                    }

                    // Check if passwords match
                    if (newPasswordInput && confirmPasswordInput && newPasswordInput.value !== confirmPasswordInput.value) {
                        isValid = false;
                        errorText = 'Passwords do not match.';
                    }

                    // Display error if validation fails
                    if (!isValid && errorMessage) {
                        e.preventDefault();
                        errorMessage.style.display = 'block';
                        errorMessage.innerHTML = '<i class="fas fa-exclamation-triangle message-icon"></i> ' + errorText;
                    }
                });
            }
        });
    </script>
</body>
</html>
