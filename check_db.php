<?php
// Database connection
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "dict_sdn_db";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
}

// Check if targets table exists
$result = $conn->query("SHOW TABLES LIKE 'targets'");
if ($result->num_rows == 0) {
    echo "Table 'targets' does not exist!\n";
} else {
    echo "Table 'targets' exists.\n";
    
    // Check table structure
    $result = $conn->query("DESCRIBE targets");
    if ($result) {
        echo "Table structure:\n";
        while ($row = $result->fetch_assoc()) {
            echo $row['Field'] . " - " . $row['Type'] . " - " . ($row['Null'] == 'YES' ? 'NULL' : 'NOT NULL') . "\n";
        }
    } else {
        echo "Error getting table structure: " . $conn->error . "\n";
    }
}

// Close connection
$conn->close();
?>
