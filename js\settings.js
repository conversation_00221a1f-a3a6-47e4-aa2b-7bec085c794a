/**
 * Settings Menu and Profile Management
 * 
 * This script handles the settings popup menu, update profile modal,
 * and logout confirmation functionality.
 */
document.addEventListener('DOMContentLoaded', () => {
    // --- Settings Popup Logic ---
    const settingsBtn = document.getElementById('settingsBtn');
    const settingsMenu = document.getElementById('settingsMenuPopup');
    const updateProfileBtn = document.getElementById('updateProfileBtn');
    const logoutBtn = document.getElementById('logoutBtn');
    
    // Add modals to the global modals object if it exists
    if (typeof modals === 'undefined') {
        window.modals = {};
    }
    
    // Add the settings-related modals
    modals.updateProfileModal = document.getElementById('updateProfileModal');
    modals.logoutConfirmModal = document.getElementById('logoutConfirmModal');
    
    // Toggle settings popup
    if (settingsBtn && settingsMenu) {
        settingsBtn.addEventListener('click', (event) => {
            event.preventDefault();
            event.stopPropagation(); // Prevent click from immediately closing menu
            const isVisible = settingsMenu.classList.toggle('visible');
            settingsBtn.setAttribute('aria-expanded', isVisible);
        });
        
        // Close menu if clicking outside
        document.addEventListener('click', (event) => {
            if (settingsMenu?.classList.contains('visible') && 
                !settingsBtn.contains(event.target) && 
                !settingsMenu.contains(event.target)) {
                settingsMenu.classList.remove('visible');
                settingsBtn.setAttribute('aria-expanded', 'false');
            }
        });
    }
    
    // Update Profile button in settings
    if (updateProfileBtn) {
        updateProfileBtn.addEventListener('click', (e) => {
            e.preventDefault();
            settingsMenu?.classList.remove('visible');
            settingsBtn?.setAttribute('aria-expanded', 'false');
            openUpdateProfileModal();
        });
    }
    
    // Logout button in settings
    if (logoutBtn) {
        logoutBtn.addEventListener('click', (e) => {
            e.preventDefault();
            settingsMenu?.classList.remove('visible');
            settingsBtn?.setAttribute('aria-expanded', 'false');
            openModal('logoutConfirmModal');
        });
    }
    
    // --- Modal Functions ---
    function openModal(modalId) {
        const modal = modals[modalId];
        if (!modal) {
            console.error("Modal not found:", modalId);
            return;
        }
        
        modal.classList.add('visible');
        document.body.style.overflow = 'hidden';
    }
    
    function closeModal(modalId) {
        const modal = modals[modalId];
        if (modal) {
            modal.classList.remove('visible');
        }
        
        // Check if any modals are still visible
        const anyVisible = Object.values(modals).some(m => m?.classList.contains('visible'));
        if (!anyVisible) {
            document.body.style.overflow = '';
        }
    }
    
    // --- Update Profile Modal Logic ---
    async function openUpdateProfileModal() {
        const modalId = 'updateProfileModal';
        openModal(modalId);
        
        const modal = modals[modalId];
        if (!modal) return;
        
        // Show loading indicator, hide form and error
        const loadingIndicator = modal.querySelector('#updateProfileLoadingIndicator');
        const errorIndicator = modal.querySelector('#updateProfileErrorIndicator');
        const profileForm = modal.querySelector('#updateProfileForm');
        const saveButton = modal.querySelector('#saveProfileButton');
        
        loadingIndicator.classList.add('active');
        loadingIndicator.style.display = 'block';
        errorIndicator.style.display = 'none';
        profileForm.style.display = 'none';
        saveButton.disabled = true;
        
        try {
            // Fetch current user data
            const response = await fetch(ajaxHandlerUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=getCurrentUser'
            });
            
            const result = await response.json();
            
            loadingIndicator.style.display = 'none';
            
            if (result.success && result.data) {
                // Populate form with user data
                populateProfileForm(result.data);
                profileForm.style.display = 'block';
                saveButton.disabled = false;
            } else {
                // Show error
                const errorSpan = errorIndicator.querySelector('span');
                if (errorSpan) {
                    errorSpan.textContent = result.message || 'Failed to load profile data.';
                }
                errorIndicator.style.display = 'flex';
            }
        } catch (error) {
            loadingIndicator.style.display = 'none';
            const errorSpan = errorIndicator.querySelector('span');
            if (errorSpan) {
                errorSpan.textContent = 'Network error. Please try again.';
            }
            errorIndicator.style.display = 'flex';
            console.error('Error fetching profile:', error);
        }
    }
    
    // Function to populate profile form with user data
    function populateProfileForm(userData) {
        document.getElementById('profile_user_id').value = userData.user_id || '';
        document.getElementById('profile_username').value = userData.username || '';
        document.getElementById('profile_full_name').value = userData.full_name || '';
        document.getElementById('profile_email').value = userData.email || '';
        // Reset password fields
        document.getElementById('profile_password').value = '';
        document.getElementById('profile_confirm_password').value = '';
    }
    
    // --- Update Profile Form Submission ---
    const updateProfileForm = document.getElementById('updateProfileForm');
    const updateProfileErrorDiv = document.getElementById('updateProfileError');
    const saveProfileButton = document.getElementById('saveProfileButton');
    
    if (saveProfileButton && updateProfileForm) {
        saveProfileButton.addEventListener('click', async (e) => {
            e.preventDefault();
            
            // Validate form
            if (!updateProfileForm.checkValidity()) {
                updateProfileForm.reportValidity();
                return;
            }
            
            // Check if passwords match
            const password = document.getElementById('profile_password').value;
            const confirmPassword = document.getElementById('profile_confirm_password').value;
            
            if (password && password !== confirmPassword) {
                updateProfileErrorDiv.textContent = 'Passwords do not match.';
                updateProfileErrorDiv.style.display = 'block';
                return;
            }
            
            // Disable save button and show loading state
            saveProfileButton.disabled = true;
            saveProfileButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            updateProfileErrorDiv.style.display = 'none';
            
            // Collect form data
            const formData = new FormData(updateProfileForm);
            formData.append('action', 'updateProfile');
            
            try {
                // Send update request
                const response = await fetch(ajaxHandlerUrl, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification(result.message || 'Profile updated successfully!', 'success');
                    closeModal('updateProfileModal');
                    
                    // Update sidebar user information
                    if (result.data && result.data.username && result.data.full_name) {
                        const usernameElement = document.querySelector('.app-title span');
                        const fullNameElement = document.querySelector('.app-title strong');
                        
                        if (usernameElement) usernameElement.textContent = result.data.username;
                        if (fullNameElement) fullNameElement.textContent = result.data.full_name;
                    }
                } else {
                    updateProfileErrorDiv.textContent = result.message || 'An unknown error occurred.';
                    updateProfileErrorDiv.style.display = 'block';
                    saveProfileButton.disabled = false;
                    saveProfileButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                }
            } catch (error) {
                updateProfileErrorDiv.textContent = 'Network error. Please try again.';
                updateProfileErrorDiv.style.display = 'block';
                saveProfileButton.disabled = false;
                saveProfileButton.innerHTML = '<i class="fas fa-save"></i> Save Changes';
                console.error('Error updating profile:', error);
            }
        });
    }
    
    // --- Logout Confirmation ---
    const confirmLogoutButton = document.getElementById('confirmLogoutButton');
    
    if (confirmLogoutButton) {
        confirmLogoutButton.addEventListener('click', () => {
            window.location.href = 'logout.php';
        });
    }
    
    // --- Modal Close Buttons ---
    document.querySelectorAll('.close-modal').forEach(button => {
        button.addEventListener('click', () => {
            const modalId = button.getAttribute('data-modal-id');
            if (modalId) {
                closeModal(modalId);
            }
        });
    });
    
    // Close modal when clicking outside
    document.querySelectorAll('.modal').forEach(modal => {
        modal.addEventListener('click', (event) => {
            if (event.target === modal) {
                const modalId = Object.keys(modals).find(key => modals[key] === modal);
                if (modalId) {
                    closeModal(modalId);
                }
            }
        });
    });
});

// Helper function to show notifications (if not already defined)
if (typeof showNotification !== 'function') {
    function showNotification(message, type = 'info') {
        const notificationElement = document.getElementById('notification');
        const notificationMessageElement = document.getElementById('notificationMessage');
        
        if (!notificationElement || !notificationMessageElement) return;
        
        if (window.notificationTimeout) clearTimeout(window.notificationTimeout);
        
        notificationMessageElement.textContent = message;
        notificationElement.className = `notification ${type}`;
        notificationElement.style.display = 'block';
        
        window.notificationTimeout = setTimeout(() => {
            notificationElement.style.display = 'none';
        }, 5000);
    }
}
