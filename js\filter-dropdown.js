/**
 * Filter Dropdown JS - Simplified version for always-visible filters
 * This script handles the filter functionality for the datatable
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all filter containers to be always visible
    document.querySelectorAll('.filter-dropdown-container').forEach(container => {
        // Make sure filters are always visible
        container.style.display = 'block';
        container.setAttribute('data-filter-open', 'true');
    });

    // Handle filter form submissions
    document.querySelectorAll('.filter-form').forEach(form => {
        form.addEventListener('submit', function(event) {
            // Let the form submit normally - the page will reload with the filtered results
            // No need to store state since filters are always visible
        });
    });

    // Handle clear filters links
    document.querySelectorAll('.clear-filters-btn').forEach(link => {
        link.addEventListener('click', function(event) {
            // Let the link navigate normally - the page will reload with cleared filters
        });
    });

    // Add event listeners to modal buttons to ensure filters stay visible
    document.querySelectorAll('.modal-trigger, .btn-edit, .btn-delete, .btn-upload, .btn-view-files').forEach(button => {
        button.addEventListener('click', function() {
            // When any modal button is clicked, make sure all filters remain visible
            document.querySelectorAll('.filter-dropdown-container').forEach(container => {
                container.style.display = 'block';
            });
        });
    });
});
