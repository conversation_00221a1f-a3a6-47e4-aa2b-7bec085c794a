<?php
/**
 * Reusable modals component for the DICT SDN Office application
 *
 * This file contains common modals that are used across multiple pages,
 * such as the Update Profile modal and Logout Confirmation modal.
 *
 * Usage:
 * 1. Include this file at the end of your page before closing the body tag
 * 2. Make sure to include settings.js after this file
 */
?>

<!-- Update Profile Modal -->
<div id="updateProfileModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2><i class="fas fa-user-edit"></i> Update Profile</h2>
            <button type="button" class="close-modal" data-modal-id="updateProfileModal">&times;</button>
        </div>
        <div class="modal-body">
            <div id="updateProfileLoadingIndicator" class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i> Loading profile data...
            </div>
            <div id="updateProfileErrorIndicator" class="error-indicator" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i> <span>Could not load profile data.</span>
            </div>
            <form id="updateProfileForm" class="form-grid" style="display: none;">
                <input type="hidden" id="profile_user_id" name="user_id">
                <div class="form-field">
                    <label for="profile_username">Username</label>
                    <input type="text" id="profile_username" name="username" required>
                </div>
                <div class="form-field">
                    <label for="profile_full_name">Full Name</label>
                    <input type="text" id="profile_full_name" name="full_name" required>
                </div>
                <div class="form-field">
                    <label for="profile_email">Email</label>
                    <input type="email" id="profile_email" name="email" required>
                </div>
                <div class="password-section">
                    <div class="form-field">
                        <label for="profile_password">New Password</label>
                        <input type="password" id="profile_password" name="password">
                        <small>Leave blank to keep current password</small>
                    </div>
                    <div class="form-field">
                        <label for="profile_confirm_password">Confirm New Password</label>
                        <input type="password" id="profile_confirm_password" name="confirm_password">
                    </div>
                </div>
                <div id="updateProfileError" class="form-error" style="display: none;"></div>
            </form>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary close-modal" data-modal-id="updateProfileModal">Cancel</button>
            <button type="button" id="saveProfileButton" class="btn btn-primary">
                <i class="fas fa-save"></i> Save Changes
            </button>
        </div>
    </div>
</div>

<!-- Logout Confirm Modal -->
<div id="logoutConfirmModal" class="modal small">
    <div class="modal-content">
        <div class="modal-header">
            <h2>Confirm Logout</h2>
            <button type="button" class="close-modal" data-modal-id="logoutConfirmModal">&times;</button>
        </div>
        <div class="modal-body">
            <div class="warning-icon-center"><i class="fas fa-exclamation-triangle warning-icon"></i></div>
            <p>Are you sure you want to log out?</p>
            <p class="warning-message">You will need to log in again to access the system.</p>
        </div>
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary close-modal" data-modal-id="logoutConfirmModal">Cancel</button>
            <button type="button" id="confirmLogoutButton" class="btn btn-danger">Log Out</button>
        </div>
    </div>
</div>
