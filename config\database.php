<?php
// config/database.php

$dbHost = 'localhost'; // Or your database host
$dbUser = 'root';      // Your database username
$dbPass = '';          // Your database password
$dbName = 'dict_sdn_db'; // Your database name

// Establish the connection
$conn = mysqli_connect($dbHost, $dbUser, $dbPass, $dbName);

// Check connection
if (!$conn) {
    // In a real application, log this error securely and avoid exposing details.
    // For development, die() is okay, but provide a clear message.
    error_log("Database Connection Failed: " . mysqli_connect_error()); // Log the error
    die("Database connection failed. Please check server logs or contact the administrator."); // User-friendly message
}

// Optional: Set character set (good practice)
mysqli_set_charset($conn, "utf8mb4");

// The $conn variable is now available to any script that includes this file.
// We don't close the connection here; it will be closed by the script that includes this file.
?>